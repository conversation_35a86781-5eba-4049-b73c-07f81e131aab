<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRM Portal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #4CAF50;
            background: #f9f9f9;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 HRM Portal System Test</h1>
        
        <div class="test-item">
            <h3>✅ React App Status</h3>
            <p>Frontend: <a href="http://localhost:5175" target="_blank">http://localhost:5175</a></p>
            <button onclick="testFrontend()">Test Frontend</button>
        </div>

        <div class="test-item">
            <h3>✅ API Server Status</h3>
            <p>Backend: <a href="http://localhost:1240" target="_blank">http://localhost:1240</a></p>
            <button onclick="testAPI()">Test API</button>
        </div>

        <div class="test-item">
            <h3>🔑 Test Login Credentials</h3>
            <p><strong>Admin:</strong> 3000 / Akaash@123</p>
            <p><strong>HR:</strong> 2000 / Akaash@123</p>
            <p><strong>Employee:</strong> 226170 / Akaash@123</p>
            <button onclick="testLogin()">Test Login</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function testFrontend() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-item"><h3>Testing Frontend...</h3></div>';
            
            fetch('http://localhost:5175')
                .then(response => {
                    if (response.ok) {
                        results.innerHTML = '<div class="test-item"><h3>✅ Frontend is working!</h3><p>React app is accessible</p></div>';
                    } else {
                        results.innerHTML = '<div class="test-item error"><h3>❌ Frontend Error</h3><p>Status: ' + response.status + '</p></div>';
                    }
                })
                .catch(error => {
                    results.innerHTML = '<div class="test-item error"><h3>❌ Frontend Connection Error</h3><p>' + error.message + '</p></div>';
                });
        }

        function testAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-item"><h3>Testing API...</h3></div>';
            
            fetch('http://localhost:1240/Users')
                .then(response => response.json())
                .then(data => {
                    results.innerHTML = '<div class="test-item"><h3>✅ API is working!</h3><p>Found ' + data.length + ' users in database</p></div>';
                })
                .catch(error => {
                    results.innerHTML = '<div class="test-item error"><h3>❌ API Connection Error</h3><p>' + error.message + '</p></div>';
                });
        }

        function testLogin() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-item"><h3>Testing Login...</h3></div>';
            
            fetch('http://localhost:1240/Users')
                .then(response => response.json())
                .then(users => {
                    const testUser = users.find(user => user.User_id === 3000);
                    if (testUser && testUser.Password === 'Akaash@123') {
                        results.innerHTML = '<div class="test-item"><h3>✅ Login Test Passed!</h3><p>Admin credentials are valid</p></div>';
                    } else {
                        results.innerHTML = '<div class="test-item error"><h3>❌ Login Test Failed</h3><p>Credentials not found</p></div>';
                    }
                })
                .catch(error => {
                    results.innerHTML = '<div class="test-item error"><h3>❌ Login Test Error</h3><p>' + error.message + '</p></div>';
                });
        }

        // Auto-run tests on page load
        window.onload = function() {
            setTimeout(testFrontend, 500);
            setTimeout(testAPI, 1000);
            setTimeout(testLogin, 1500);
        };
    </script>
</body>
</html>
