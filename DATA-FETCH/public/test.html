<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Server Test Page</h1>
        <p>If you can see this, the Vite server is working!</p>
        <p>✅ HTML is loading</p>
        <p>✅ CSS is working</p>
        <p>✅ Server is responding</p>
        <hr>
        <p><strong>Next:</strong> Try the main React app at <a href="/" style="color: #FFD700;">localhost:5179</a></p>
    </div>
</body>
</html>
