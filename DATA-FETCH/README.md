# HRM Portal - Enhanced React Application

A modern, interactive Human Resource Management portal built with React, TypeScript, and cutting-edge web technologies.

## 🚀 Features

### ✨ Modern UI/UX
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Interactive Components**: Smooth animations and transitions
- **Dark Mode Support**: Automatic theme detection with manual override
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support

### 🔐 Authentication & Security
- **Secure Login System**: JWT-based authentication with role-based access
- **Context-based State Management**: Centralized auth state with React Context
- **Protected Routes**: Role-based route protection (Admin, HR, Employee)
- **Session Management**: Automatic token refresh and logout

### 📊 Data Visualization
- **Interactive Dashboards**: Real-time charts and statistics
- **Custom Chart Components**: Bar charts, pie charts, and line graphs
- **Activity Monitoring**: Live user activity tracking
- **Performance Metrics**: System health and usage analytics

### 🎯 Advanced Form Handling
- **Smart Validation**: Real-time form validation with user-friendly error messages
- **Dynamic Forms**: Conditional fields based on user input
- **Search & Filter**: Advanced filtering with debounced search
- **Optimistic Updates**: Instant UI feedback with error rollback

### ⚡ Performance Optimizations
- **Code Splitting**: Lazy-loaded routes and components
- **Service Worker**: Offline functionality and intelligent caching
- **Virtual Scrolling**: Efficient rendering of large lists
- **Memoization**: Optimized re-renders with React.memo and useMemo
- **Bundle Optimization**: Tree shaking and dynamic imports

## 🛠 Technology Stack

### Frontend
- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **React Router DOM** - Client-side routing
- **CSS Variables** - Modern styling with custom properties
- **Service Workers** - Offline functionality and caching

### State Management
- **React Context** - Centralized state management
- **Custom Hooks** - Reusable stateful logic
- **Local Storage** - Persistent client-side storage

### Development Tools
- **Vite** - Fast build tool and dev server
- **ESLint** - Code linting and quality
- **TypeScript** - Static type checking

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hrm-portal
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Demo Credentials

| Role | User ID | Password |
|------|---------|----------|
| Admin | 226170 | Akaash@123 |
| Employee | 226171 | Akaash@123 |

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 Design System

### Color Palette
- **Primary**: #646cff (Interactive elements)
- **Success**: #10b981 (Success states)
- **Danger**: #ef4444 (Error states)
- **Warning**: #f59e0b (Warning states)
- **Info**: #3b82f6 (Information)

### Typography
- **Font Family**: Inter, system fonts
- **Headings**: 600-700 weight
- **Body**: 400-500 weight
- **Scale**: Modular scale with consistent spacing

### Spacing System
- **XS**: 0.25rem (4px)
- **SM**: 0.5rem (8px)
- **MD**: 1rem (16px)
- **LG**: 1.5rem (24px)
- **XL**: 2rem (32px)

## 📱 Progressive Web App (PWA)

- **Offline Functionality**: Works without internet connection
- **App-like Experience**: Install on mobile devices
- **Background Sync**: Sync data when connection is restored
- **Push Notifications**: Real-time updates
- **Responsive Design**: Optimized for all screen sizes

---

**Built with ❤️ using modern web technologies**
