<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .test-item.success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .test-item.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 New Interactive HRM Portal Test</h1>
        <p>Test the brand new user-friendly HRM website:</p>
        
        <button onclick="testWebsite()">Test Website</button>
        <button onclick="openWebsite()">Open Website</button>
        <button onclick="checkConsole()">Check Console</button>

        <div id="results"></div>
    </div>

    <script>
        function testWebsite() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="test-item"><h3>Testing New HRM Website...</h3></div>';

            fetch('http://localhost:5174/')
                .then(response => {
                    if (response.ok) {
                        results.innerHTML = `
                            <div class="test-item success">
                                <h3>✅ New HRM Website is Running!</h3>
                                <p>Status: ${response.status} ${response.statusText}</p>
                                <p>🎉 Interactive HRM Portal is ready!</p>
                                <p>Features: Login System, Role-based Dashboards, Modern UI</p>
                                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px;">
                                    <strong>Demo Accounts:</strong><br>
                                    • Employee: emp001 / password123<br>
                                    • Admin: admin001 / password123<br>
                                    • HR: hr001 / password123
                                </div>
                            </div>
                        `;
                    } else {
                        results.innerHTML = `
                            <div class="test-item error">
                                <h3>❌ Website Error</h3>
                                <p>Status: ${response.status} ${response.statusText}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    results.innerHTML = `
                        <div class="test-item error">
                            <h3>❌ Connection Error</h3>
                            <p>${error.message}</p>
                            <p>Make sure the development server is running on port 5174</p>
                        </div>
                    `;
                });
        }

        function openWebsite() {
            window.open('http://localhost:5174/', '_blank');
        }

        function checkConsole() {
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="test-item">
                    <h3>📋 Console Check Instructions</h3>
                    <p>1. Open the website in a new tab</p>
                    <p>2. Press F12 to open Developer Tools</p>
                    <p>3. Check the Console tab for any errors</p>
                    <p>4. Look for red error messages</p>
                    <p>5. If no errors, the website is working correctly!</p>
                </div>
            `;
        }

        // Auto-test on page load
        window.onload = function() {
            testWebsite();
        };
    </script>
</body>
</html>
