/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f8fafc;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app {
  min-height: 100vh;
}

/* Login Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 10px;
}

.logo-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo h1 {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: #666;
  font-size: 0.9rem;
}

.login-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 8px;
  border-left: 4px solid #e74c3c;
}

.login-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.demo-section {
  border-top: 1px solid #e1e5e9;
  padding-top: 20px;
}

.demo-section h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1rem;
  text-align: center;
}

.demo-users {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.demo-user {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-user:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.demo-user.employee {
  border-left: 4px solid #28a745;
}

.demo-user.admin {
  border-left: 4px solid #dc3545;
}

.demo-user.hr {
  border-left: 4px solid #ffc107;
}

.demo-info strong {
  display: block;
  font-weight: 600;
  color: #333;
}

.demo-info span {
  display: block;
  color: #666;
  font-size: 0.9rem;
}

.demo-info .role {
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 2px;
}

.demo-user.employee .role {
  color: #28a745;
}

.demo-user.admin .role {
  color: #dc3545;
}

.demo-user.hr .role {
  color: #ffc107;
}

.demo-note {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

/* Dashboard Styles */
.dashboard {
  min-height: 100vh;
  background: #f8fafc;
}

.header {
  background: white;
  padding: 15px 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid #667eea;
}

.header-left .logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left .logo span {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.user-role {
  color: #666;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.logout-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.dashboard-content {
  display: flex;
  min-height: calc(100vh - 80px);
}

.sidebar {
  width: 250px;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px 0;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 0 20px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 0.9rem;
  color: #666;
  width: 100%;
}

.nav-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-label {
  font-weight: 500;
}

.main-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.content-section {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
  border-left: 4px solid #667eea;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 15px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.stat-content p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* Dashboard Widgets */
.dashboard-widgets {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.widget {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.widget h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.2rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.activity-icon {
  font-size: 1.2rem;
}

.activity-time {
  margin-left: auto;
  color: #666;
  font-size: 0.8rem;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.action-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Profile Styles */
.profile-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.profile-avatar-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.profile-info h2 {
  margin-bottom: 5px;
  color: #333;
}

.profile-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.edit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-left: auto;
}

.edit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row label {
  font-weight: 600;
  color: #333;
}

.detail-row span {
  color: #666;
}

/* Attendance Styles */
.attendance-summary {
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.summary-card h3 {
  margin-bottom: 20px;
  color: #333;
}

.summary-stats {
  display: flex;
  gap: 30px;
}

.summary-item {
  text-align: center;
}

.summary-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 5px;
}

.summary-label {
  color: #666;
  font-size: 0.9rem;
}

.attendance-table {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.attendance-table h3 {
  margin-bottom: 20px;
  color: #333;
}

.attendance-table table {
  width: 100%;
  border-collapse: collapse;
}

.attendance-table th,
.attendance-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.attendance-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #333;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status.present {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status.weekend {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* Leave Management Styles */
.leave-balance {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.leave-balance h3 {
  margin-bottom: 20px;
  color: #333;
}

.balance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.balance-card {
  padding: 20px;
  border-radius: 10px;
  background: #f8fafc;
  border-left: 4px solid #667eea;
}

.balance-card h4 {
  margin-bottom: 10px;
  color: #333;
}

.balance-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.used {
  color: #dc3545;
}

.remaining {
  color: #28a745;
  font-weight: 600;
}

.balance-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.balance-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.leave-request {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.leave-request h3 {
  margin-bottom: 20px;
  color: #333;
}

.leave-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Payroll Styles */
.payroll-summary {
  margin-bottom: 30px;
}

.salary-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.salary-card h3 {
  margin-bottom: 20px;
  color: #333;
}

.salary-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.salary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.salary-item:last-child {
  border-bottom: none;
}

.salary-item.total {
  font-weight: 700;
  font-size: 1.1rem;
  border-top: 2px solid #667eea;
  padding-top: 15px;
  margin-top: 10px;
}

.positive {
  color: #28a745;
  font-weight: 600;
}

.negative {
  color: #dc3545;
  font-weight: 600;
}

.payslip-history {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.payslip-history h3 {
  margin-bottom: 20px;
  color: #333;
}

.payslip-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.payslip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.download-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.download-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

/* Placeholder Styles */
.placeholder {
  background: white;
  padding: 60px 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.placeholder h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.placeholder p {
  color: #666;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-card {
    padding: 30px 20px;
  }

  .header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }

  .dashboard-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    padding: 15px 0;
  }

  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    padding: 0 20px;
    gap: 10px;
  }

  .nav-item {
    white-space: nowrap;
    min-width: 120px;
  }

  .main-content {
    padding: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-widgets {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    flex-direction: column;
    gap: 15px;
  }

  .balance-cards {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .payslip-item {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .demo-users {
    max-height: 200px;
    overflow-y: auto;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .attendance-table {
    overflow-x: auto;
  }
}

/* New Dashboard Styles */
.dashboard-header-new {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.dashboard-title h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: #1a202c;
}

.breadcrumb {
  color: #718096;
  font-size: 0.9rem;
  margin-top: 5px;
}

.dashboard-actions {
  display: flex;
  gap: 12px;
}

.btn-secondary {
  padding: 10px 20px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #4a5568;
  cursor: pointer;
  font-weight: 500;
}

.btn-primary {
  padding: 10px 20px;
  background: #667eea;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  font-weight: 500;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.overview-stats {
  display: flex;
  gap: 20px;
}

.stat-card-new {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  flex: 1;
  position: relative;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 5px;
}

.stat-label {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.stat-change {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.stat-change.positive {
  color: #38a169;
}

.stat-icon-small {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 1.2rem;
}

.attendance-widget {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.widget-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.time-selector {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #4a5568;
  font-size: 0.9rem;
}

.attendance-display {
  text-align: center;
}

.time-display {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.time-display span {
  font-size: 1.2rem;
  opacity: 0.8;
}

.shift-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.shift-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.shift-item:last-child {
  border-bottom: none;
}

.shift-item .label {
  color: #718096;
  font-size: 0.9rem;
}

.shift-item .value {
  color: #1a202c;
  font-weight: 500;
  font-size: 0.9rem;
}

.announcements-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.see-more {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 0.9rem;
}

.announcement-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.announcement-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.announcement-content {
  flex: 1;
}

.announcement-title {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 5px;
}

.announcement-meta {
  display: flex;
  gap: 15px;
  color: #718096;
  font-size: 0.85rem;
}

.quick-actions-new {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.quick-actions-new h3 {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background: #f7fafc;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-card:hover {
  background: #edf2f7;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 1.5rem;
}

.action-card span {
  color: #4a5568;
  font-weight: 500;
  font-size: 0.9rem;
}

/* New Profile Styles */
.profile-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.profile-header-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.profile-header-new h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #1a202c;
}

.invite-btn {
  padding: 12px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
}

.profile-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.portfolio-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.view-all {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 0.9rem;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.portfolio-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.portfolio-preview {
  height: 120px;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.portfolio-placeholder {
  font-size: 2rem;
  color: #a0aec0;
}

.portfolio-info {
  padding: 15px;
}

.portfolio-info h4 {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1a202c;
}

.portfolio-info p {
  margin: 0;
  font-size: 0.8rem;
  color: #718096;
}

.skills-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.skills-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-tag {
  padding: 8px 16px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #4a5568;
}

.recommendations-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.recommendations-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.recommendation-item {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.recommender-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

.recommender-info {
  margin-bottom: 8px;
}

.recommendation-content p {
  margin: 0 0 10px 0;
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.5;
}

.view-all-small {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 0.85rem;
}

.documents-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.documents-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.document-icon {
  font-size: 1.5rem;
  color: #667eea;
}

.document-info {
  flex: 1;
}

.document-name {
  display: block;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 3px;
}

.document-size {
  display: block;
  font-size: 0.8rem;
  color: #718096;
}

.download-icon {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #a0aec0;
  cursor: pointer;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.profile-card-new {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.profile-avatar-new {
  margin: 0 auto 15px auto;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-card-new h2 {
  margin: 0 0 5px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1a202c;
}

.profile-title {
  margin: 0 0 15px 0;
  color: #667eea;
  font-weight: 500;
}

.profile-description {
  margin: 0 0 20px 0;
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.5;
}

.contact-icons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.contact-icon {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: #f7fafc;
  cursor: pointer;
  font-size: 1.1rem;
  transition: background 0.2s ease;
}

.contact-icon:hover {
  background: #edf2f7;
}

.experience-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.experience-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
}

.experience-item {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
}

.experience-item:last-child {
  margin-bottom: 0;
}

.company-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.experience-details {
  flex: 1;
}

.experience-details h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
}

.experience-details > p {
  margin: 0 0 5px 0;
  color: #718096;
  font-size: 0.85rem;
}

.location {
  font-size: 0.8rem;
  color: #a0aec0;
}

.role-info {
  margin-top: 10px;
}

.role-info strong {
  display: block;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 5px;
}

.role-info p {
  margin: 0;
  color: #4a5568;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Responsive styles for new components */
@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .overview-stats {
    flex-direction: column;
  }

  .dashboard-header-new {
    flex-direction: column;
    gap: 15px;
  }

  .profile-layout {
    grid-template-columns: 1fr;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .skills-grid {
    justify-content: center;
  }

  .experience-item {
    flex-direction: column;
    text-align: center;
  }

  .contact-icons {
    flex-wrap: wrap;
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mt-8 {
  margin-top: 2rem;
}