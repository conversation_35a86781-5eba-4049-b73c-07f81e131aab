import React, { type ReactNode } from 'react';
import { AuthProvider } from './AuthContext';
import { AppProvider } from './AppContext';

// Combined Providers Component
interface ProvidersProps {
  children: ReactNode;
}

export const Providers: React.FC<ProvidersProps> = ({ children }) => {
  return (
    <AppProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </AppProvider>
  );
};

// Re-export hooks and providers
export { useAuth } from './AuthContext';
export { useApp } from './AppContext';
export { AuthProvider } from './AuthContext';
export { AppProvider } from './AppContext';
