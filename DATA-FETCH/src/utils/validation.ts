// Validation utilities for HRM system

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateMobileNumber = (mobile: string): boolean => {
  // Remove all non-digit characters
  const cleanMobile = mobile.replace(/\D/g, '');
  
  // Check if it's a valid Indian mobile number (10 digits)
  return cleanMobile.length === 10 && /^[6-9]/.test(cleanMobile);
};

export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateName = (name: string): boolean => {
  return name.trim().length >= 2 && /^[a-zA-Z\s]+$/.test(name.trim());
};

export const validateEmployeeId = (id: string | number): boolean => {
  const idStr = String(id);
  return /^\d+$/.test(idStr) && idStr.length >= 3;
};

export const validateDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};

export const validateAge = (birthDate: string): {
  isValid: boolean;
  age?: number;
  error?: string;
} => {
  if (!validateDate(birthDate)) {
    return { isValid: false, error: 'Invalid date format' };
  }
  
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  if (age < 18) {
    return { isValid: false, error: 'Employee must be at least 18 years old' };
  }
  
  if (age > 65) {
    return { isValid: false, error: 'Employee age cannot exceed 65 years' };
  }
  
  return { isValid: true, age };
};

// File validation for profile pictures
export const validateImageFile = (file: File): {
  isValid: boolean;
  error?: string;
} => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { isValid: false, error: 'Please select a valid image file' };
  }
  
  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > maxSize) {
    return { isValid: false, error: 'File size should be less than 5MB' };
  }
  
  // Check image dimensions (optional)
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      if (img.width < 100 || img.height < 100) {
        resolve({ isValid: false, error: 'Image should be at least 100x100 pixels' });
      } else if (img.width > 2000 || img.height > 2000) {
        resolve({ isValid: false, error: 'Image should not exceed 2000x2000 pixels' });
      } else {
        resolve({ isValid: true });
      }
    };
    img.onerror = () => {
      resolve({ isValid: false, error: 'Invalid image file' });
    };
    img.src = URL.createObjectURL(file);
  }) as any;
};

// Sanitize input to prevent XSS
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < and > characters
    .trim();
};

// Format mobile number for display
export const formatMobileNumber = (mobile: string): string => {
  const cleanMobile = mobile.replace(/\D/g, '');
  if (cleanMobile.length === 10) {
    return `+91 ${cleanMobile.slice(0, 5)} ${cleanMobile.slice(5)}`;
  }
  return mobile;
};

// Format employee ID for display
export const formatEmployeeId = (id: string | number): string => {
  return String(id).padStart(6, '0');
};
