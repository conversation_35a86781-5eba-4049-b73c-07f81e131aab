import React, { useState } from 'react';
import './EmployeeDashboard.css';

interface User {
  id: string;
  name: string;
  role: 'employee' | 'admin' | 'hr';
  email: string;
  department?: string;
  position?: string;
}

interface EmployeeDashboardProps {
  user: User;
  onLogout: () => void;
}

const EmployeeDashboard: React.FC<EmployeeDashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [leaveForm, setLeaveForm] = useState({
    type: 'vacation',
    startDate: '',
    endDate: '',
    reason: ''
  });

  // Sample data
  const attendanceData = [
    { date: '2024-01-15', checkIn: '09:00', checkOut: '17:30', status: 'Present' },
    { date: '2024-01-14', checkIn: '09:15', checkOut: '17:45', status: 'Present' },
    { date: '2024-01-13', checkIn: '09:00', checkOut: '17:30', status: 'Present' },
    { date: '2024-01-12', checkIn: '-', checkOut: '-', status: 'Weekend' },
    { date: '2024-01-11', checkIn: '-', checkOut: '-', status: 'Weekend' },
  ];

  const leaveBalance = {
    vacation: { used: 5, total: 20 },
    sick: { used: 2, total: 10 },
    personal: { used: 1, total: 5 }
  };

  const payrollData = {
    basicSalary: 75000,
    allowances: 15000,
    deductions: 8000,
    netSalary: 82000,
    lastPayDate: '2024-01-01'
  };

  const handleLeaveSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('Leave request submitted successfully!');
    setLeaveForm({ type: 'vacation', startDate: '', endDate: '', reason: '' });
  };

  const renderOverview = () => (
    <div className="overview-content">
      <div className="welcome-section">
        <h2>Welcome back, {user.name}! 👋</h2>
        <p>Here's your dashboard overview for today</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">📅</div>
          <div className="stat-info">
            <h3>22</h3>
            <p>Days Present This Month</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">🏖️</div>
          <div className="stat-info">
            <h3>{leaveBalance.vacation.total - leaveBalance.vacation.used}</h3>
            <p>Vacation Days Left</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <h3>${payrollData.netSalary.toLocaleString()}</h3>
            <p>Last Month Salary</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">⏰</div>
          <div className="stat-info">
            <h3>8.5h</h3>
            <p>Average Daily Hours</p>
          </div>
        </div>
      </div>

      <div className="quick-actions">
        <h3>Quick Actions</h3>
        <div className="action-buttons">
          <button className="action-btn" onClick={() => setActiveTab('attendance')}>
            📊 View Attendance
          </button>
          <button className="action-btn" onClick={() => setActiveTab('leave')}>
            📝 Request Leave
          </button>
          <button className="action-btn" onClick={() => setActiveTab('payroll')}>
            💳 View Payroll
          </button>
          <button className="action-btn" onClick={() => setActiveTab('profile')}>
            👤 Update Profile
          </button>
        </div>
      </div>
    </div>
  );

  const renderAttendance = () => (
    <div className="attendance-content">
      <h2>📊 Attendance Record</h2>
      <div className="attendance-summary">
        <div className="summary-item">
          <span className="summary-label">This Month:</span>
          <span className="summary-value">22 days present</span>
        </div>
        <div className="summary-item">
          <span className="summary-label">Average Hours:</span>
          <span className="summary-value">8.5 hours/day</span>
        </div>
      </div>
      
      <div className="attendance-table">
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Check In</th>
              <th>Check Out</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {attendanceData.map((record, index) => (
              <tr key={index}>
                <td>{record.date}</td>
                <td>{record.checkIn}</td>
                <td>{record.checkOut}</td>
                <td>
                  <span className={`status ${record.status.toLowerCase()}`}>
                    {record.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderLeave = () => (
    <div className="leave-content">
      <h2>🏖️ Leave Management</h2>
      
      <div className="leave-balance">
        <h3>Leave Balance</h3>
        <div className="balance-grid">
          {Object.entries(leaveBalance).map(([type, balance]) => (
            <div key={type} className="balance-card">
              <h4>{type.charAt(0).toUpperCase() + type.slice(1)}</h4>
              <div className="balance-info">
                <span className="used">{balance.used} used</span>
                <span className="remaining">{balance.total - balance.used} remaining</span>
              </div>
              <div className="balance-bar">
                <div 
                  className="balance-fill" 
                  style={{ width: `${(balance.used / balance.total) * 100}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="leave-request">
        <h3>Request Leave</h3>
        <form onSubmit={handleLeaveSubmit} className="leave-form">
          <div className="form-row">
            <div className="form-group">
              <label>Leave Type</label>
              <select 
                value={leaveForm.type} 
                onChange={(e) => setLeaveForm({...leaveForm, type: e.target.value})}
              >
                <option value="vacation">Vacation</option>
                <option value="sick">Sick Leave</option>
                <option value="personal">Personal</option>
              </select>
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Start Date</label>
              <input 
                type="date" 
                value={leaveForm.startDate}
                onChange={(e) => setLeaveForm({...leaveForm, startDate: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <label>End Date</label>
              <input 
                type="date" 
                value={leaveForm.endDate}
                onChange={(e) => setLeaveForm({...leaveForm, endDate: e.target.value})}
                required
              />
            </div>
          </div>
          <div className="form-group">
            <label>Reason</label>
            <textarea 
              value={leaveForm.reason}
              onChange={(e) => setLeaveForm({...leaveForm, reason: e.target.value})}
              placeholder="Please provide a reason for your leave request"
              rows={3}
              required
            />
          </div>
          <button type="submit" className="submit-btn">Submit Request</button>
        </form>
      </div>
    </div>
  );

  const renderPayroll = () => (
    <div className="payroll-content">
      <h2>💰 Payroll Information</h2>
      
      <div className="payroll-summary">
        <div className="salary-card">
          <h3>Current Salary Breakdown</h3>
          <div className="salary-details">
            <div className="salary-item">
              <span>Basic Salary:</span>
              <span>${payrollData.basicSalary.toLocaleString()}</span>
            </div>
            <div className="salary-item">
              <span>Allowances:</span>
              <span className="positive">+${payrollData.allowances.toLocaleString()}</span>
            </div>
            <div className="salary-item">
              <span>Deductions:</span>
              <span className="negative">-${payrollData.deductions.toLocaleString()}</span>
            </div>
            <div className="salary-item total">
              <span>Net Salary:</span>
              <span>${payrollData.netSalary.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="payroll-history">
        <h3>Recent Payslips</h3>
        <div className="payslip-list">
          <div className="payslip-item">
            <span>January 2024</span>
            <span>${payrollData.netSalary.toLocaleString()}</span>
            <button className="download-btn">📄 Download</button>
          </div>
          <div className="payslip-item">
            <span>December 2023</span>
            <span>${payrollData.netSalary.toLocaleString()}</span>
            <button className="download-btn">📄 Download</button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProfile = () => (
    <div className="profile-content">
      <h2>👤 Profile Information</h2>
      
      <div className="profile-card">
        <div className="profile-header">
          <div className="profile-avatar">
            {user.name.charAt(0).toUpperCase()}
          </div>
          <div className="profile-info">
            <h3>{user.name}</h3>
            <p>{user.position}</p>
            <p>{user.department}</p>
          </div>
        </div>
        
        <div className="profile-details">
          <div className="detail-item">
            <label>Employee ID:</label>
            <span>{user.id}</span>
          </div>
          <div className="detail-item">
            <label>Email:</label>
            <span>{user.email}</span>
          </div>
          <div className="detail-item">
            <label>Department:</label>
            <span>{user.department}</span>
          </div>
          <div className="detail-item">
            <label>Position:</label>
            <span>{user.position}</span>
          </div>
          <div className="detail-item">
            <label>Join Date:</label>
            <span>January 15, 2022</span>
          </div>
        </div>
        
        <button className="edit-profile-btn">✏️ Edit Profile</button>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'attendance': return renderAttendance();
      case 'leave': return renderLeave();
      case 'payroll': return renderPayroll();
      case 'profile': return renderProfile();
      default: return renderOverview();
    }
  };

  return (
    <div className="employee-dashboard">
      <header className="dashboard-header">
        <div className="header-left">
          <h1>🏢 Employee Portal</h1>
          <span className="user-info">{user.name} - {user.department}</span>
        </div>
        <button className="logout-btn" onClick={onLogout}>
          🚪 Logout
        </button>
      </header>

      <nav className="dashboard-nav">
        {[
          { id: 'overview', label: '📊 Overview', icon: '📊' },
          { id: 'attendance', label: '📅 Attendance', icon: '📅' },
          { id: 'leave', label: '🏖️ Leave', icon: '🏖️' },
          { id: 'payroll', label: '💰 Payroll', icon: '💰' },
          { id: 'profile', label: '👤 Profile', icon: '👤' }
        ].map(tab => (
          <button
            key={tab.id}
            className={`nav-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>
    </div>
  );
};

export default EmployeeDashboard;
