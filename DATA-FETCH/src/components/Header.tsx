import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Button from './ui/Button';
import { cn } from '../utils';
import './Header.css';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="hrm-header">
      <div className="container header-container">
        {/* Logo */}
        <Link to="/" className="header-logo-link">
          <div className="header-logo">
            <svg className="header-logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 2L2 7l10 5 10-5-10-5z"/>
              <path d="M2 17l10 5 10-5"/>
              <path d="M2 12l10 5 10-5"/>
            </svg>
            <span className="header-logo-text">HRM Portal</span>
          </div>
        </Link>

        {/* Desktop Navigation */}
        <nav className="header-nav">
          {isAuthenticated ? (
            <div className="header-user-menu">
              <div className="header-user-info">
                <div className="header-user-avatar">
                  {user?.name?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div className="header-user-details">
                  <span className="header-user-name">{user?.name}</span>
                  <span className="header-user-role">{user?.role}</span>
                </div>
              </div>
              <Button
                variant="secondary"
                size="small"
                onClick={handleLogout}
                className="header-logout-btn"
              >
                Logout
              </Button>
            </div>
          ) : (
            <Button
              variant="primary"
              onClick={() => navigate('/login')}
              className="header-login-btn"
            >
              Portal Login
            </Button>
          )}
        </nav>

        {/* Mobile Menu Button */}
        <button
          className={cn('header-mobile-toggle', isMenuOpen && 'header-mobile-toggle--open')}
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          <span className="header-mobile-toggle-line"></span>
          <span className="header-mobile-toggle-line"></span>
          <span className="header-mobile-toggle-line"></span>
        </button>

        {/* Mobile Menu */}
        <div className={cn('header-mobile-menu', isMenuOpen && 'header-mobile-menu--open')}>
          {isAuthenticated ? (
            <div className="header-mobile-user">
              <div className="header-mobile-user-info">
                <div className="header-user-avatar">
                  {user?.name?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div>
                  <div className="header-user-name">{user?.name}</div>
                  <div className="header-user-role">{user?.role}</div>
                </div>
              </div>
              <Button
                variant="danger"
                size="small"
                onClick={handleLogout}
                className="header-mobile-logout"
              >
                Logout
              </Button>
            </div>
          ) : (
            <Button
              variant="primary"
              onClick={() => {
                navigate('/login');
                setIsMenuOpen(false);
              }}
              className="header-mobile-login"
            >
              Portal Login
            </Button>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;