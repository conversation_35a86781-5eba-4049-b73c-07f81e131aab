import React from 'react';
import { useNavigate } from 'react-router-dom';
import './HeroSection.css';

const HeroSection: React.FC = () => {
  const navigate = useNavigate();
  return (
    <section className="hero-section">
      <div className="hero-overlay"></div>
      <div className="hero-content">
        <h1>Welcome to Your HRM Portal</h1>
        <p>Simplifying HR, Payroll, and Employee Management</p>
        <button className="hero-btn" onClick={() => navigate('/login')}>Access Your Portal</button>
      </div>
    </section>
  );
};

export default HeroSection;