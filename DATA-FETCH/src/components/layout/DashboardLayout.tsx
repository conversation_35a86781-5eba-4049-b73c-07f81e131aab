import React from 'react';
import type { ReactNode } from 'react';
import Sidebar from './Sidebar';
import type { NavItem } from './Sidebar';
import './DashboardLayout.css';

interface DashboardLayoutProps { navItems: NavItem[]; children: ReactNode; }

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ navItems, children }) => (
  <div className="dashboard-layout">
    <Sidebar navItems={navItems} />
    <main className="dashboard-content">{children}</main>
  </div>
);

export default DashboardLayout;