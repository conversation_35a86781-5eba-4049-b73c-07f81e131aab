import React from 'react';
import { NavLink } from 'react-router-dom';
import './Sidebar.css';

export interface NavItem { path: string; name: string; icon: string; }
interface SidebarProps { navItems: NavItem[]; }

const Sidebar: React.FC<SidebarProps> = ({ navItems }) => (
  <aside className="sidebar">
    <h3 className="sidebar-header">HRM Menu</h3>
    <nav className="sidebar-nav">
      <ul>{navItems.map(item => <li key={item.name}><NavLink to={item.path} className={({ isActive }) => isActive ? 'active' : ''}><span className="nav-icon">{item.icon}</span>{item.name}</NavLink></li>)}</ul>
    </nav>
  </aside>
);

export default Sidebar;