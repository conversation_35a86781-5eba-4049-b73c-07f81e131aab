/* Chart Container */
.chart-container {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

/* Bar Chart Styles */
.bar-chart {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  gap: var(--spacing-sm);
}

.bar-chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80px;
}

.bar-chart-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
  text-align: center;
  word-break: break-word;
}

.bar-chart-bar-container {
  height: 150px;
  width: 100%;
  display: flex;
  align-items: end;
  justify-content: center;
}

.bar-chart-bar {
  width: 100%;
  max-width: 40px;
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  position: relative;
  transition: all var(--transition-normal);
  display: flex;
  align-items: end;
  justify-content: center;
  padding-bottom: var(--spacing-xs);
}

.bar-chart-bar:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.bar-chart-value {
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Pie Chart Styles */
.pie-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.pie-chart-svg {
  width: 200px;
  height: 200px;
}

.pie-chart-legend {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.pie-chart-legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.pie-chart-legend-color {
  width: 1rem;
  height: 1rem;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.pie-chart-legend-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

/* Line Chart Styles */
.line-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.line-chart-svg {
  width: 100%;
  height: 150px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-gray-50);
}

.line-chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 var(--spacing-sm);
}

.line-chart-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

/* Stats Card Styles */
.stats-card {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  transition: all var(--transition-normal);
}

.stats-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stats-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.stats-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-card-icon {
  width: 2rem;
  height: 2rem;
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: var(--spacing-sm);
  line-height: 1;
}

.stats-card-change {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  font-weight: 500;
}

.stats-card-change--increase {
  color: var(--color-success);
}

.stats-card-change--decrease {
  color: var(--color-danger);
}

.stats-card-change-icon {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-container {
    padding: var(--spacing-md);
  }
  
  .bar-chart {
    height: 150px;
    gap: var(--spacing-xs);
  }
  
  .bar-chart-bar-container {
    height: 120px;
  }
  
  .bar-chart-label {
    font-size: 0.625rem;
  }
  
  .pie-chart-svg {
    width: 150px;
    height: 150px;
  }
  
  .stats-card {
    padding: var(--spacing-md);
  }
  
  .stats-card-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .pie-chart-legend {
    font-size: 0.75rem;
  }
  
  .line-chart-svg {
    height: 120px;
  }
  
  .stats-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .chart-container,
  .stats-card {
    background: var(--color-surface);
    border-color: var(--color-border);
  }
  
  .line-chart-svg {
    background: var(--color-gray-800);
    border-color: var(--color-border);
  }
}
