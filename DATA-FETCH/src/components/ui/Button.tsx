import React from 'react';
import { ButtonProps } from '../../types';
import { cn } from '../../utils';
import './Button.css';

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  children,
  className,
  type = 'button',
  fullWidth = false,
  style,
  ...props
}) => {
  const baseClasses = 'btn';
  const variantClasses = `btn--${variant}`;
  const sizeClasses = `btn--${size}`;
  const stateClasses = cn(
    disabled && 'btn--disabled',
    loading && 'btn--loading',
    fullWidth && 'btn--full-width'
  );

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  return (
    <button
      type={type}
      className={cn(baseClasses, variantClasses, sizeClasses, stateClasses, className)}
      onClick={handleClick}
      disabled={disabled || loading}
      style={style}
      {...props}
    >
      {loading && (
        <span className="btn__spinner">
          <svg className="btn__spinner-icon" viewBox="0 0 24 24">
            <circle
              className="btn__spinner-circle"
              cx="12"
              cy="12"
              r="10"
              fill="none"
              strokeWidth="2"
            />
          </svg>
        </span>
      )}
      <span className={cn('btn__content', loading && 'btn__content--loading')}>
        {children}
      </span>
    </button>
  );
};

export default Button;
