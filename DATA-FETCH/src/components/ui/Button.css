/* Button Base Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  border-radius: 0.5rem;
  font-family: inherit;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Button Sizes */
.btn--small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn--medium {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.btn--large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

/* Button Variants */
.btn--primary {
  background-color: #646cff;
  color: white;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.btn--primary:hover:not(.btn--disabled):not(.btn--loading) {
  background-color: #535bf2;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transform: translateY(-1px);
}

.btn--secondary {
  background-color: #f1f5f9;
  color: #1e293b;
  border: 1px solid #e2e8f0;
}

.btn--secondary:hover:not(.btn--disabled):not(.btn--loading) {
  background-color: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.btn--danger {
  background-color: #ef4444;
  color: white;
}

.btn--danger:hover:not(.btn--disabled):not(.btn--loading) {
  background-color: #dc2626;
  transform: translateY(-1px);
}

.btn--success {
  background-color: #10b981;
  color: white;
}

.btn--success:hover:not(.btn--disabled):not(.btn--loading) {
  background-color: #059669;
  transform: translateY(-1px);
}

.btn--warning {
  background-color: #f59e0b;
  color: white;
}

.btn--warning:hover:not(.btn--disabled):not(.btn--loading) {
  background-color: #d97706;
  transform: translateY(-1px);
}

/* Button States */
.btn--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn--loading {
  cursor: wait;
}

.btn__content--loading {
  opacity: 0.7;
}

/* Spinner Styles */
.btn__spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.btn__spinner-icon {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

.btn__spinner-circle {
  stroke: currentColor;
  stroke-linecap: round;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: spinner-dash 2s ease-in-out infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .btn--large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .btn--medium {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}

/* Full Width Button */
.btn--full-width {
  width: 100%;
}
