/* Input Group Styles */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

/* Label Styles */
.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.input-label--required::after {
  content: ' *';
  color: #ef4444;
}

/* Input Wrapper */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* Input Base Styles */
.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #1f2937;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out;
}

.input::placeholder {
  color: #9ca3af;
}

.input:focus {
  outline: none;
  border-color: #646cff;
  box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

/* Input States */
.input--focused {
  border-color: #646cff;
  box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

.input--error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input--error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input--disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Password Toggle Button */
.input-password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s ease-in-out;
}

.input-password-toggle:hover:not(:disabled) {
  color: #374151;
}

.input-password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.input-icon {
  width: 1.25rem;
  height: 1.25rem;
  stroke-width: 2;
}

/* Error Message Styles */
.input-error {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.input-error-icon {
  width: 1rem;
  height: 1rem;
  stroke-width: 2;
  flex-shrink: 0;
}

.input-error-text {
  line-height: 1.25;
}

/* Responsive Design */
@media (max-width: 640px) {
  .input {
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
  }
  
  .input-password-toggle {
    right: 0.625rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .input-label {
    color: #d1d5db;
  }
  
  .input {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .input::placeholder {
    color: #6b7280;
  }
  
  .input--disabled {
    background-color: #111827;
    color: #6b7280;
  }
  
  .input-password-toggle {
    color: #9ca3af;
  }
  
  .input-password-toggle:hover:not(:disabled) {
    color: #d1d5db;
  }
}
