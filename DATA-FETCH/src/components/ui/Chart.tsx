import React from 'react';
import { cn } from '../../utils';
import './Chart.css';

// Chart Data Interfaces
interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

interface ChartProps {
  data: ChartDataPoint[];
  title?: string;
  className?: string;
}

// Bar Chart Component
export const BarChart: React.FC<ChartProps> = ({ data, title, className }) => {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <div className={cn('chart-container', className)}>
      {title && <h3 className="chart-title">{title}</h3>}
      <div className="bar-chart">
        {data.map((item, index) => (
          <div key={index} className="bar-chart-item">
            <div className="bar-chart-label">{item.label}</div>
            <div className="bar-chart-bar-container">
              <div
                className="bar-chart-bar"
                style={{
                  height: `${(item.value / maxValue) * 100}%`,
                  backgroundColor: item.color || 'var(--color-primary)',
                }}
              >
                <span className="bar-chart-value">{item.value}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Pie Chart Component
export const PieChart: React.FC<ChartProps> = ({ data, title, className }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let cumulativePercentage = 0;
  
  const colors = [
    'var(--color-primary)',
    'var(--color-success)',
    'var(--color-warning)',
    'var(--color-danger)',
    'var(--color-info)',
  ];
  
  return (
    <div className={cn('chart-container', className)}>
      {title && <h3 className="chart-title">{title}</h3>}
      <div className="pie-chart">
        <svg className="pie-chart-svg" viewBox="0 0 200 200">
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const startAngle = (cumulativePercentage / 100) * 360;
            const endAngle = ((cumulativePercentage + percentage) / 100) * 360;
            
            const startAngleRad = (startAngle - 90) * (Math.PI / 180);
            const endAngleRad = (endAngle - 90) * (Math.PI / 180);
            
            const largeArcFlag = percentage > 50 ? 1 : 0;
            
            const x1 = 100 + 80 * Math.cos(startAngleRad);
            const y1 = 100 + 80 * Math.sin(startAngleRad);
            const x2 = 100 + 80 * Math.cos(endAngleRad);
            const y2 = 100 + 80 * Math.sin(endAngleRad);
            
            const pathData = [
              `M 100 100`,
              `L ${x1} ${y1}`,
              `A 80 80 0 ${largeArcFlag} 1 ${x2} ${y2}`,
              'Z'
            ].join(' ');
            
            cumulativePercentage += percentage;
            
            return (
              <path
                key={index}
                d={pathData}
                fill={item.color || colors[index % colors.length]}
                stroke="white"
                strokeWidth="2"
              />
            );
          })}
        </svg>
        <div className="pie-chart-legend">
          {data.map((item, index) => (
            <div key={index} className="pie-chart-legend-item">
              <div
                className="pie-chart-legend-color"
                style={{
                  backgroundColor: item.color || colors[index % colors.length],
                }}
              />
              <span className="pie-chart-legend-label">
                {item.label}: {item.value} ({Math.round((item.value / total) * 100)}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Line Chart Component (Simple version)
interface LineChartProps {
  data: { x: string; y: number }[];
  title?: string;
  className?: string;
}

export const LineChart: React.FC<LineChartProps> = ({ data, title, className }) => {
  const maxY = Math.max(...data.map(d => d.y));
  const minY = Math.min(...data.map(d => d.y));
  const range = maxY - minY || 1;
  
  const points = data.map((point, index) => {
    const x = (index / (data.length - 1)) * 300;
    const y = 150 - ((point.y - minY) / range) * 120;
    return `${x},${y}`;
  }).join(' ');
  
  return (
    <div className={cn('chart-container', className)}>
      {title && <h3 className="chart-title">{title}</h3>}
      <div className="line-chart">
        <svg className="line-chart-svg" viewBox="0 0 300 150">
          <polyline
            points={points}
            fill="none"
            stroke="var(--color-primary)"
            strokeWidth="2"
          />
          {data.map((point, index) => {
            const x = (index / (data.length - 1)) * 300;
            const y = 150 - ((point.y - minY) / range) * 120;
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="4"
                fill="var(--color-primary)"
              />
            );
          })}
        </svg>
        <div className="line-chart-labels">
          {data.map((point, index) => (
            <span key={index} className="line-chart-label">
              {point.x}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ReactNode;
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon,
  className,
}) => {
  return (
    <div className={cn('stats-card', className)}>
      <div className="stats-card-header">
        <div className="stats-card-title">{title}</div>
        {icon && <div className="stats-card-icon">{icon}</div>}
      </div>
      <div className="stats-card-value">{value}</div>
      {change && (
        <div className={cn('stats-card-change', `stats-card-change--${change.type}`)}>
          <span className="stats-card-change-icon">
            {change.type === 'increase' ? '↗' : '↘'}
          </span>
          <span className="stats-card-change-value">
            {Math.abs(change.value)}%
          </span>
        </div>
      )}
    </div>
  );
};
