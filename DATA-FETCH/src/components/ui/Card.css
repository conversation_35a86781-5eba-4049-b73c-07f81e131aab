/* Card Base Styles */
.card {
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

/* Card Padding Variants */
.card--padding-none {
  padding: 0;
}

.card--padding-small {
  padding: 1rem;
}

.card--padding-medium {
  padding: 1.5rem;
}

.card--padding-large {
  padding: 2rem;
}

/* Card Shadow Variants */
.card--shadow-none {
  box-shadow: none;
}

.card--shadow-small {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.card--shadow-medium {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.card--shadow-large {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Card Interactive States */
.card--hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.card--clickable {
  cursor: pointer;
}

.card--clickable:hover {
  border-color: #d1d5db;
}

.card--clickable:active {
  transform: translateY(0);
}

/* Card Header */
.card-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  background-color: #fafafa;
}

.card-header:first-child {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
  margin: 0;
  font-weight: 600;
  color: #111827;
}

/* Card Body */
.card-body {
  padding: 1.5rem;
  flex: 1;
}

.card-body p:first-child {
  margin-top: 0;
}

.card-body p:last-child {
  margin-bottom: 0;
}

/* Card Footer */
.card-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #f3f4f6;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.card-footer:last-child {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

/* Card with no padding adjustments for header/body/footer */
.card--padding-none .card-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

.card--padding-none .card-body {
  padding: 1.5rem;
}

.card--padding-none .card-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
}

/* Responsive Design */
@media (max-width: 640px) {
  .card--padding-large {
    padding: 1.5rem;
  }
  
  .card--padding-medium {
    padding: 1rem;
  }
  
  .card-header {
    padding: 1rem 1rem 0.75rem 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .card-footer {
    padding: 0.75rem 1rem 1rem 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .card--padding-none .card-header {
    padding: 1rem 1rem 0.75rem 1rem;
  }
  
  .card--padding-none .card-body {
    padding: 1rem;
  }
  
  .card--padding-none .card-footer {
    padding: 0.75rem 1rem 1rem 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .card-header {
    background-color: #111827;
    border-bottom-color: #374151;
  }
  
  .card-header h1,
  .card-header h2,
  .card-header h3,
  .card-header h4,
  .card-header h5,
  .card-header h6 {
    color: #f9fafb;
  }
  
  .card-footer {
    background-color: #111827;
    border-top-color: #374151;
  }
  
  .card--clickable:hover {
    border-color: #4b5563;
  }
}
