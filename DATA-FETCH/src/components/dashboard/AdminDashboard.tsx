import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import type { User } from '../../types/User';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface AdminDashboardProps {
  currentUser?: User | null;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ currentUser }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>('dashboard');
  const [user, setUser] = useState<User | null>(currentUser || null);

  useEffect(() => {
    if (!currentUser) {
      const userData = localStorage.getItem('currentUser');
      if (userData) {
        setUser(JSON.parse(userData));
      } else {
        navigate('/login');
      }
    }
  }, [currentUser, navigate]);

  const handleLogout = () => {
    localStorage.removeItem('currentUser');
    navigate('/');
  };

  const sidebarItems = [
    { id: 'dashboard', icon: '📊', label: 'Dashboard' },
    { id: 'employees', icon: '👥', label: 'Employee Management' },
    { id: 'departments', icon: '🏢', label: 'Departments' },
    { id: 'payroll', icon: '💰', label: 'Payroll' },
    { id: 'reports', icon: '📈', label: 'Reports & Analytics' },
    { id: 'settings', icon: '⚙️', label: 'System Settings' }
  ];

  const statsCards = [
    { title: 'Total Employees', value: '1,247', icon: '👥', color: '#4CAF50', change: '+12%' },
    { title: 'Active Projects', value: '89', icon: '📋', color: '#2196F3', change: '+5%' },
    { title: 'Monthly Payroll', value: '$2.4M', icon: '💰', color: '#FF9800', change: '+8%' },
    { title: 'Departments', value: '24', icon: '🏢', color: '#9C27B0', change: '+2%' }
  ];

  const recentActivities = [
    { user: 'John Doe', action: 'submitted leave request', time: '2 hours ago', type: 'leave' },
    { user: 'Sarah Wilson', action: 'completed onboarding', time: '4 hours ago', type: 'onboard' },
    { user: 'Mike Johnson', action: 'updated profile', time: '6 hours ago', type: 'profile' },
    { user: 'Emily Davis', action: 'clocked in', time: '8 hours ago', type: 'attendance' }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: '#f5f7fa',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        background: 'linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%)',
        color: 'white',
        padding: '1rem 2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div style={{
            width: '40px',
            height: '40px',
            background: 'rgba(255,255,255,0.2)',
            borderRadius: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.2rem'
          }}>
            👑
          </div>
          <div>
            <h1 style={{ margin: 0, fontSize: '1.5rem' }}>Admin Dashboard</h1>
            <p style={{ margin: 0, opacity: 0.8, fontSize: '0.9rem' }}>
              Welcome back, {user ? user.name : 'Administrator'}
            </p>
          </div>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '20px',
            padding: '0.5rem 1rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <div style={{
              width: '30px',
              height: '30px',
              background: '#FFD700',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.8rem'
            }}>
              {user ? user.name.charAt(0).toUpperCase() : 'A'}
            </div>
            <span>{user ? user.name : 'Admin User'}</span>
            <span style={{ fontSize: '0.8rem', opacity: 0.7 }}>
              (ID: {user ? user.User_id : 'N/A'})
            </span>
          </div>
          <Button
            onClick={handleLogout}
            variant="secondary"
            size="small"
          >
            Logout
          </Button>
        </div>
      </header>

      <div style={{ display: 'flex' }}>
        {/* Sidebar */}
        <nav style={{
          width: '250px',
          background: 'white',
          minHeight: 'calc(100vh - 80px)',
          padding: '1rem',
          boxShadow: '2px 0 10px rgba(0,0,0,0.05)'
        }}>
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              style={{
                width: '100%',
                padding: '1rem',
                margin: '0.25rem 0',
                border: 'none',
                borderRadius: '10px',
                background: activeTab === item.id ? 'linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%)' : 'transparent',
                color: activeTab === item.id ? 'white' : '#333',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                fontSize: '0.95rem',
                transition: 'all 0.3s ease'
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>{item.icon}</span>
              {item.label}
            </button>
          ))}
        </nav>

        {/* Main Content */}
        <main style={{
          flex: 1,
          padding: '2rem'
        }}>
          {activeTab === 'dashboard' && (
            <div>
              <h2 style={{ marginBottom: '2rem', color: '#333' }}>Dashboard Overview</h2>
              
              {/* Stats Cards */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1.5rem',
                marginBottom: '2rem'
              }}>
                {statsCards.map((stat, index) => (
                  <Card key={index} className="text-center">
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '1rem'
                    }}>
                      <div style={{
                        width: '50px',
                        height: '50px',
                        background: `${stat.color}20`,
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1.5rem'
                      }}>
                        {stat.icon}
                      </div>
                      <span style={{
                        background: '#e8f5e8',
                        color: '#2e7d32',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '12px',
                        fontSize: '0.8rem',
                        fontWeight: 'bold'
                      }}>
                        {stat.change}
                      </span>
                    </div>
                    <h3 style={{
                      fontSize: '2rem',
                      fontWeight: 'bold',
                      margin: '0 0 0.5rem 0',
                      color: '#333'
                    }}>
                      {stat.value}
                    </h3>
                    <p style={{
                      color: '#666',
                      margin: 0,
                      fontSize: '0.9rem'
                    }}>
                      {stat.title}
                    </p>
                  </Card>
                ))}
              </div>

              {/* Recent Activities */}
              <Card>
                <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Recent Activities</h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {recentActivities.map((activity, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1rem',
                      padding: '1rem',
                      background: '#f8f9fa',
                      borderRadius: '10px'
                    }}>
                      <div style={{
                        width: '40px',
                        height: '40px',
                        background: activity.type === 'leave' ? '#ff9800' : 
                                   activity.type === 'onboard' ? '#4caf50' :
                                   activity.type === 'profile' ? '#2196f3' : '#9c27b0',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '1.2rem'
                      }}>
                        {activity.type === 'leave' ? '📅' : 
                         activity.type === 'onboard' ? '👋' :
                         activity.type === 'profile' ? '👤' : '⏰'}
                      </div>
                      <div style={{ flex: 1 }}>
                        <p style={{ margin: 0, fontWeight: '500', color: '#333' }}>
                          <strong>{activity.user}</strong> {activity.action}
                        </p>
                        <p style={{ margin: 0, fontSize: '0.8rem', color: '#666' }}>
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'employees' && (
            <div>
              <h2 style={{ marginBottom: '2rem', color: '#333' }}>Employee Management</h2>
              <Card>
                <p>Employee management features coming soon...</p>
              </Card>
            </div>
          )}

          {/* Add other tab contents as needed */}
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
