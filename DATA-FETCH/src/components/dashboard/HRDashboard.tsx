import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import type { User } from '../../types/User';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface HRDashboardProps {
  currentUser?: User | null;
}

const HRDashboard: React.FC<HRDashboardProps> = ({ currentUser }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>('dashboard');
  const [user, setUser] = useState<User | null>(currentUser || null);

  useEffect(() => {
    if (!currentUser) {
      const userData = localStorage.getItem('currentUser');
      if (userData) {
        setUser(JSON.parse(userData));
      } else {
        navigate('/login');
      }
    }
  }, [currentUser, navigate]);

  const handleLogout = () => {
    localStorage.removeItem('currentUser');
    navigate('/');
  };

  const sidebarItems = [
    { id: 'dashboard', icon: '📊', label: 'Dashboard' },
    { id: 'recruitment', icon: '🎯', label: 'Recruitment' },
    { id: 'employees', icon: '👥', label: 'Employee Records' },
    { id: 'leave', icon: '📅', label: 'Leave Management' },
    { id: 'performance', icon: '⭐', label: 'Performance' },
    { id: 'training', icon: '📚', label: 'Training & Development' }
  ];

  const hrStats = [
    { title: 'Open Positions', value: '23', icon: '🎯', color: '#f44336' },
    { title: 'Pending Leaves', value: '15', icon: '📅', color: '#ff9800' },
    { title: 'New Hires (Month)', value: '8', icon: '👋', color: '#4caf50' },
    { title: 'Training Sessions', value: '12', icon: '📚', color: '#2196f3' }
  ];

  const quickActions = [
    { title: 'Post New Job', icon: '📝', color: '#4caf50' },
    { title: 'Review Applications', icon: '📋', color: '#2196f3' },
    { title: 'Schedule Interview', icon: '🗓️', color: '#ff9800' },
    { title: 'Generate Report', icon: '📊', color: '#9c27b0' }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: '#f5f7fa',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
        color: 'white',
        padding: '1rem 2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div style={{
            width: '40px',
            height: '40px',
            background: 'rgba(255,255,255,0.2)',
            borderRadius: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.2rem'
          }}>
            👨‍💼
          </div>
          <div>
            <h1 style={{ margin: 0, fontSize: '1.5rem' }}>HR Dashboard</h1>
            <p style={{ margin: 0, opacity: 0.8, fontSize: '0.9rem' }}>
              Welcome, {user ? user.name : 'HR Manager'}
            </p>
          </div>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '20px',
            padding: '0.5rem 1rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <div style={{
              width: '30px',
              height: '30px',
              background: '#FFD700',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '0.8rem'
            }}>
              {user ? user.name.charAt(0).toUpperCase() : 'H'}
            </div>
            <span>{user ? user.name : 'HR User'}</span>
            <span style={{ fontSize: '0.8rem', opacity: 0.7 }}>
              (ID: {user ? user.User_id : 'N/A'})
            </span>
          </div>
          <Button
            onClick={handleLogout}
            variant="secondary"
            size="small"
          >
            Logout
          </Button>
        </div>
      </header>

      <div style={{ display: 'flex' }}>
        {/* Sidebar */}
        <nav style={{
          width: '250px',
          background: 'white',
          minHeight: 'calc(100vh - 80px)',
          padding: '1rem',
          boxShadow: '2px 0 10px rgba(0,0,0,0.05)'
        }}>
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              style={{
                width: '100%',
                padding: '1rem',
                margin: '0.25rem 0',
                border: 'none',
                borderRadius: '10px',
                background: activeTab === item.id ? 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)' : 'transparent',
                color: activeTab === item.id ? 'white' : '#333',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                fontSize: '0.95rem',
                transition: 'all 0.3s ease'
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>{item.icon}</span>
              {item.label}
            </button>
          ))}
        </nav>

        {/* Main Content */}
        <main style={{ flex: 1, padding: '2rem' }}>
          <h2 style={{ marginBottom: '2rem', color: '#333' }}>HR Dashboard Overview</h2>
          
          {/* HR Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            {hrStats.map((stat, index) => (
              <Card key={index} className="text-center">
                <div style={{
                  fontSize: '2rem',
                  marginBottom: '1rem'
                }}>
                  {stat.icon}
                </div>
                <h3 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  margin: '0 0 0.5rem 0',
                  color: stat.color
                }}>
                  {stat.value}
                </h3>
                <p style={{ color: '#666', margin: 0 }}>{stat.title}</p>
              </Card>
            ))}
          </div>

          {/* Quick Actions */}
          <Card>
            <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Quick Actions</h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem'
            }}>
              {quickActions.map((action, index) => (
                <button key={index} style={{
                  background: `${action.color}10`,
                  border: `2px solid ${action.color}30`,
                  borderRadius: '10px',
                  padding: '1rem',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{action.icon}</div>
                  <div style={{ color: action.color, fontWeight: 'bold' }}>{action.title}</div>
                </button>
              ))}
            </div>
          </Card>

          {/* Recent HR Activities */}
          <Card className="mt-8">
            <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Recent HR Activities</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {[
                { activity: 'New job posting for Senior Developer', time: '2 hours ago', type: 'job' },
                { activity: 'Interview scheduled with John Smith', time: '4 hours ago', type: 'interview' },
                { activity: 'Leave request approved for Sarah Wilson', time: '6 hours ago', type: 'leave' },
                { activity: 'Performance review completed for Mike Johnson', time: '1 day ago', type: 'performance' }
              ].map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '1rem',
                  background: '#f8f9fa',
                  borderRadius: '10px'
                }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    background: item.type === 'job' ? '#4caf50' : 
                               item.type === 'interview' ? '#2196f3' :
                               item.type === 'leave' ? '#ff9800' : '#9c27b0',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.2rem'
                  }}>
                    {item.type === 'job' ? '💼' : 
                     item.type === 'interview' ? '🗣️' :
                     item.type === 'leave' ? '📅' : '⭐'}
                  </div>
                  <div style={{ flex: 1 }}>
                    <p style={{ margin: 0, fontWeight: '500', color: '#333' }}>
                      {item.activity}
                    </p>
                    <p style={{ margin: 0, fontSize: '0.8rem', color: '#666' }}>
                      {item.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default HRDashboard;
