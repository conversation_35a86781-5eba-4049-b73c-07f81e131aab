import React, { useState, useEffect } from 'react';
import type { EmployeeProfile as EmployeeProfileType, User } from '../types/User';

interface EmployeeProfileProps {
  currentUser: User;
  onUpdateProfile: (profileData: Partial<EmployeeProfileType>) => void;
}

const EmployeeProfile: React.FC<EmployeeProfileProps> = ({ currentUser, onUpdateProfile }) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('personal');
  const [profileData, setProfileData] = useState<EmployeeProfileType>({
    ...currentUser,
    // Default values
    firstName: currentUser.name.split(' ')[0] || '',
    lastName: currentUser.name.split(' ').slice(1).join(' ') || '',
    mobileNumber: '',
    dateOfBirth: '',
    gender: 'Male',
    maritalStatus: 'Single',
    nationality: 'Indian',
    department: 'Technology',
    designation: 'Software Developer',
    joiningDate: '2024-01-01',
    employmentType: 'Full-time',
    workLocation: 'Chennai',
    profilePicture: '',
    skills: ['JavaScript', 'React', 'TypeScript'],
    isActive: true
  });

  const [profileImage, setProfileImage] = useState<string>('');

  useEffect(() => {
    // Generate a default avatar if no profile picture
    if (!profileData.profilePicture) {
      const initials = `${profileData.firstName?.charAt(0) || ''}${profileData.lastName?.charAt(0) || ''}`;
      setProfileImage(`https://ui-avatars.com/api/?name=${initials}&background=4CAF50&color=fff&size=200`);
    } else {
      setProfileImage(profileData.profilePicture);
    }
  }, [profileData.firstName, profileData.lastName, profileData.profilePicture]);

  const handleInputChange = (field: keyof EmployeeProfileType, value: any) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    onUpdateProfile(profileData);
    setIsEditing(false);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setProfileImage(result);
        handleInputChange('profilePicture', result);
      };
      reader.onerror = () => {
        alert('Error reading file');
      };
      reader.readAsDataURL(file);
    }
  };

  const renderPersonalInfo = () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>
      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          First Name *
        </label>
        <input
          type="text"
          value={profileData.firstName || ''}
          onChange={(e) => handleInputChange('firstName', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Last Name *
        </label>
        <input
          type="text"
          value={profileData.lastName || ''}
          onChange={(e) => handleInputChange('lastName', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Mobile Number *
        </label>
        <input
          type="tel"
          value={profileData.mobileNumber || ''}
          onChange={(e) => handleInputChange('mobileNumber', e.target.value)}
          disabled={!isEditing}
          placeholder="+91 9876543210"
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Date of Birth
        </label>
        <input
          type="date"
          value={profileData.dateOfBirth || ''}
          onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Gender
        </label>
        <select
          value={profileData.gender || 'Male'}
          onChange={(e) => handleInputChange('gender', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        >
          <option value="Male">Male</option>
          <option value="Female">Female</option>
          <option value="Other">Other</option>
        </select>
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Marital Status
        </label>
        <select
          value={profileData.maritalStatus || 'Single'}
          onChange={(e) => handleInputChange('maritalStatus', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        >
          <option value="Single">Single</option>
          <option value="Married">Married</option>
          <option value="Divorced">Divorced</option>
          <option value="Widowed">Widowed</option>
        </select>
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Email Address
        </label>
        <input
          type="email"
          value={profileData.email}
          disabled={true}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: '#f5f5f5',
            color: '#666'
          }}
        />
        <small style={{ color: '#666', fontSize: '0.8rem' }}>Email cannot be changed</small>
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Nationality
        </label>
        <input
          type="text"
          value={profileData.nationality || ''}
          onChange={(e) => handleInputChange('nationality', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>
    </div>
  );

  const renderProfessionalInfo = () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>
      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Employee ID
        </label>
        <input
          type="text"
          value={profileData.User_id.toString()}
          disabled={true}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: '#f5f5f5',
            color: '#666'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Department
        </label>
        <select
          value={profileData.department || ''}
          onChange={(e) => handleInputChange('department', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        >
          <option value="">Select Department</option>
          <option value="Technology">Technology</option>
          <option value="Human Resources">Human Resources</option>
          <option value="Finance">Finance</option>
          <option value="Marketing">Marketing</option>
          <option value="Operations">Operations</option>
          <option value="Sales">Sales</option>
        </select>
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Designation
        </label>
        <input
          type="text"
          value={profileData.designation || ''}
          onChange={(e) => handleInputChange('designation', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Joining Date
        </label>
        <input
          type="date"
          value={profileData.joiningDate || ''}
          onChange={(e) => handleInputChange('joiningDate', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Employment Type
        </label>
        <select
          value={profileData.employmentType || ''}
          onChange={(e) => handleInputChange('employmentType', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        >
          <option value="Full-time">Full-time</option>
          <option value="Part-time">Part-time</option>
          <option value="Contract">Contract</option>
          <option value="Intern">Intern</option>
        </select>
      </div>

      <div>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '500', color: '#333' }}>
          Work Location
        </label>
        <input
          type="text"
          value={profileData.workLocation || ''}
          onChange={(e) => handleInputChange('workLocation', e.target.value)}
          disabled={!isEditing}
          style={{
            width: '100%',
            padding: '0.75rem',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '1rem',
            backgroundColor: isEditing ? 'white' : '#f5f5f5'
          }}
        />
      </div>
    </div>
  );

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Profile Header */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        padding: '2rem',
        marginBottom: '2rem',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        display: 'flex',
        alignItems: 'center',
        gap: '2rem'
      }}>
        {/* Profile Picture */}
        <div style={{ position: 'relative' }}>
          <img
            src={profileImage}
            alt="Profile"
            style={{
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              objectFit: 'cover',
              border: '4px solid #7b1fa2'
            }}
          />
          {isEditing && (
            <label style={{
              position: 'absolute',
              bottom: '0',
              right: '0',
              background: '#7b1fa2',
              color: 'white',
              borderRadius: '50%',
              width: '35px',
              height: '35px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              fontSize: '1.2rem'
            }}>
              📷
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: 'none' }}
              />
            </label>
          )}
        </div>

        {/* Profile Info */}
        <div style={{ flex: 1 }}>
          <h2 style={{ margin: '0 0 0.5rem 0', color: '#333', fontSize: '2rem' }}>
            {profileData.firstName} {profileData.lastName}
          </h2>
          <p style={{ margin: '0 0 0.5rem 0', color: '#666', fontSize: '1.1rem' }}>
            {profileData.designation} • {profileData.department}
          </p>
          <p style={{ margin: '0 0 1rem 0', color: '#666' }}>
            Employee ID: {profileData.User_id} • {profileData.email}
          </p>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <span style={{
              background: profileData.isActive ? '#e8f5e8' : '#ffebee',
              color: profileData.isActive ? '#2e7d32' : '#c62828',
              padding: '0.25rem 0.75rem',
              borderRadius: '20px',
              fontSize: '0.9rem',
              fontWeight: 'bold'
            }}>
              {profileData.isActive ? '🟢 Active' : '🔴 Inactive'}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{ display: 'flex', gap: '1rem' }}>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              style={{
                background: 'linear-gradient(135deg, #7b1fa2 0%, #ab47bc 100%)',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '25px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              ✏️ Edit Profile
            </button>
          ) : (
            <>
              <button
                onClick={handleSave}
                style={{
                  background: 'linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold'
                }}
              >
                💾 Save Changes
              </button>
              <button
                onClick={() => setIsEditing(false)}
                style={{
                  background: '#f44336',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold'
                }}
              >
                ❌ Cancel
              </button>
            </>
          )}
        </div>
      </div>

      {/* Profile Tabs */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        overflow: 'hidden'
      }}>
        {/* Tab Headers */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid #e0e0e0',
          background: '#f8f9fa'
        }}>
          {[
            { id: 'personal', label: 'Personal Information', icon: '👤' },
            { id: 'professional', label: 'Professional Details', icon: '💼' },
            { id: 'contact', label: 'Contact & Address', icon: '📞' },
            { id: 'documents', label: 'Documents', icon: '📄' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                flex: 1,
                padding: '1rem',
                border: 'none',
                background: activeTab === tab.id ? 'white' : 'transparent',
                color: activeTab === tab.id ? '#7b1fa2' : '#666',
                cursor: 'pointer',
                fontSize: '0.95rem',
                fontWeight: activeTab === tab.id ? 'bold' : 'normal',
                borderBottom: activeTab === tab.id ? '3px solid #7b1fa2' : '3px solid transparent',
                transition: 'all 0.3s ease'
              }}
            >
              <span style={{ marginRight: '0.5rem' }}>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div style={{ padding: '2rem' }}>
          {activeTab === 'personal' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Personal Information</h3>
              {renderPersonalInfo()}
            </div>
          )}

          {activeTab === 'professional' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Professional Details</h3>
              {renderProfessionalInfo()}
            </div>
          )}

          {activeTab === 'contact' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Contact & Address Information</h3>
              <p style={{ color: '#666', fontStyle: 'italic' }}>Contact information section coming soon...</p>
            </div>
          )}

          {activeTab === 'documents' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#333' }}>Documents & Certificates</h3>
              <p style={{ color: '#666', fontStyle: 'italic' }}>Document management section coming soon...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeProfile;
