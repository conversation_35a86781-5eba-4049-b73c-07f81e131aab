import React, { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { getDashboardStats, getRecentActivity } from '../api/dashboardService';
import type { DashboardStats, ActivityItem } from '../types';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, StatsCard } from './ui/Chart';
import Card, { CardHeader, CardBody } from './ui/Card';
import { getRelativeTime } from '../utils';
import './Dashboard.css';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch dashboard stats
        const statsResponse = await getDashboardStats();
        if (statsResponse.success && statsResponse.data) {
          setStats(statsResponse.data);
        } else {
          console.error('Failed to load dashboard statistics');
        }

        // Fetch recent activity
        const activityResponse = await getRecentActivity({ limit: 10 });
        if (activityResponse.success && activityResponse.data) {
          setActivities(activityResponse.data.activities);
        }
      } catch (error) {
        console.error('Dashboard fetch error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="dashboard-loading-spinner">
          <div className="spinner"></div>
        </div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="dashboard-error">
        <p>Failed to load dashboard data</p>
      </div>
    );
  }

  // Prepare chart data
  const userRoleData = [
    { label: 'Admin', value: 1, color: '#ef4444' },
    { label: 'HR', value: 2, color: '#f59e0b' },
    { label: 'Employee', value: stats.totalUsers - 3, color: '#10b981' },
  ];

  const activityTypeData = activities.reduce((acc, activity) => {
    const existing = acc.find(item => item.label === activity.type);
    if (existing) {
      existing.value++;
    } else {
      acc.push({ label: activity.type, value: 1 });
    }
    return acc;
  }, [] as { label: string; value: number }[]);

  return (
    <div className="dashboard">
      {/* Welcome Section */}
      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1 className="dashboard-title">
            Welcome back, {user?.name}!
          </h1>
          <p className="dashboard-subtitle">
            Here's what's happening in your organization today.
          </p>
        </div>
        <div className="dashboard-date">
          {new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="dashboard-stats">
        <StatsCard
          title="Total Users"
          value={stats.totalUsers}
          change={{ value: 12, type: 'increase' }}
          icon={
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
          }
        />
        
        <StatsCard
          title="Active Users"
          value={stats.activeUsers}
          change={{ value: 8, type: 'increase' }}
          icon={
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          }
        />
        
        <StatsCard
          title="Departments"
          value={stats.totalDepartments}
          icon={
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
              <polyline points="9,22 9,12 15,12 15,22"/>
            </svg>
          }
        />
        
        <StatsCard
          title="Recent Activity"
          value={activities.length}
          change={{ value: 5, type: 'increase' }}
          icon={
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
            </svg>
          }
        />
      </div>

      {/* Charts Section */}
      <div className="dashboard-charts">
        <div className="dashboard-chart-item">
          <PieChart
            title="Users by Role"
            data={userRoleData}
          />
        </div>
        
        <div className="dashboard-chart-item">
          <BarChart
            title="Activity Types"
            data={activityTypeData}
          />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="dashboard-activity">
        <Card>
          <CardHeader>
            <h3>Recent Activity</h3>
          </CardHeader>
          <CardBody>
            <div className="activity-list">
              {activities.map((activity) => (
                <div key={activity.id} className="activity-item">
                  <div className={`activity-icon activity-icon--${activity.type}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="activity-content">
                    <div className="activity-text">
                      <strong>{activity.user}</strong> {activity.action}
                    </div>
                    <div className="activity-time">
                      {getRelativeTime(activity.timestamp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

// Helper function to get activity icons
const getActivityIcon = (type: ActivityItem['type']) => {
  switch (type) {
    case 'login':
      return (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
          <polyline points="10,17 15,12 10,7"/>
          <line x1="15" y1="12" x2="3" y2="12"/>
        </svg>
      );
    case 'logout':
      return (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
          <polyline points="16,17 21,12 16,7"/>
          <line x1="21" y1="12" x2="9" y2="12"/>
        </svg>
      );
    case 'update':
      return (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
      );
    case 'create':
      return (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="16"/>
          <line x1="8" y1="12" x2="16" y2="12"/>
        </svg>
      );
    case 'delete':
      return (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <polyline points="3,6 5,6 21,6"/>
          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
        </svg>
      );
    default:
      return (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="16" x2="12" y2="12"/>
          <line x1="12" y1="8" x2="12.01" y2="8"/>
        </svg>
      );
  }
};

export default Dashboard;
