/* Loading Spinner Container */
.loading-spinner-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background);
  z-index: 9999;
  gap: var(--spacing-lg);
}

.loading-spinner-inline {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-lg);
}

/* Spinner Animation */
.loading-spinner {
  display: inline-block;
  position: relative;
}

.loading-spinner--small {
  width: 2rem;
  height: 2rem;
}

.loading-spinner--medium {
  width: 3rem;
  height: 3rem;
}

.loading-spinner--large {
  width: 4rem;
  height: 4rem;
}

.loading-spinner-circle {
  position: absolute;
  border: 2px solid var(--color-primary);
  opacity: 1;
  border-radius: 50%;
  animation: loading-spinner-animation 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.loading-spinner-circle:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes loading-spinner-animation {
  0% {
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    opacity: 1;
    transform: translate(-50%, -50%);
  }
  100% {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translate(0, 0);
  }
}

/* Loading Message */
.loading-spinner-message {
  color: var(--color-text-secondary);
  font-size: 1rem;
  text-align: center;
  margin: 0;
  animation: loading-message-pulse 2s ease-in-out infinite;
}

@keyframes loading-message-pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* Alternative Spinner Styles */
.loading-spinner-dots {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loading-spinner-dots div {
  position: absolute;
  top: 33px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: var(--color-primary);
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading-spinner-dots div:nth-child(1) {
  left: 8px;
  animation: loading-dots1 0.6s infinite;
}

.loading-spinner-dots div:nth-child(2) {
  left: 8px;
  animation: loading-dots2 0.6s infinite;
}

.loading-spinner-dots div:nth-child(3) {
  left: 32px;
  animation: loading-dots2 0.6s infinite;
}

.loading-spinner-dots div:nth-child(4) {
  left: 56px;
  animation: loading-dots3 0.6s infinite;
}

@keyframes loading-dots1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loading-dots3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes loading-dots2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .loading-spinner-message {
    font-size: 0.875rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .loading-spinner-fullscreen {
    background-color: var(--color-background);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner-circle {
    animation: none;
  }
  
  .loading-spinner-message {
    animation: none;
  }
  
  .loading-spinner-dots div {
    animation: none;
  }
  
  /* Show a static indicator instead */
  .loading-spinner::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    border: 2px solid var(--color-border);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
  }
}
