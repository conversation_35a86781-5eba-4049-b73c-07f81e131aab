/* ================================
   HERO SECTION - FIXED LAYOUT
   ================================ */

.hero-section {
  height: 100vh;
  width: 100vw;                  /* Make sure it spans full screen */
  background-color: #000000;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.hero-content {
  max-width: 1200px;
  padding: 0 30px;
  margin: 0 auto;
  width: 100%;
  text-align: center;
  z-index: 2;
}

/* Headline */
.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

/* Subheading */
.hero-content p {
  font-size: 1.5rem;
  margin-bottom: 36px;
  color: #cccccc;
  line-height: 1.6;
}

/* CTA Button */
.hero-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 16px 36px;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.hero-btn:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

/* Responsive tweaks */
@media (max-width: 992px) {
  .hero-content h1 {
    font-size: 3rem;
  }

  .hero-content p {
    font-size: 1.25rem;
  }

  .hero-btn {
    padding: 14px 28px;
    font-size: 1.1rem;
  }
}

@media (max-width: 600px) {
  .hero-content h1 {
    font-size: 2.2rem;
  }

  .hero-content p {
    font-size: 1.1rem;
    margin-bottom: 24px;
  }

  .hero-btn {
    padding: 12px 24px;
    font-size: 1rem;
  }
}
