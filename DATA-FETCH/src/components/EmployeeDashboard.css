.employee-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  background: white;
  padding: 20px 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid #28a745;
}

.header-left h1 {
  margin: 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.user-info {
  color: #666;
  font-size: 0.9rem;
  margin-top: 5px;
  display: block;
}

.logout-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.dashboard-nav {
  background: white;
  padding: 0 30px;
  display: flex;
  gap: 5px;
  border-bottom: 1px solid #e9ecef;
  overflow-x: auto;
}

.nav-btn {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  font-weight: 600;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.nav-btn:hover {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.nav-btn.active {
  color: #28a745;
  border-bottom-color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.dashboard-content {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Overview Styles */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.welcome-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.welcome-section h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 2rem;
}

.welcome-section p {
  color: #666;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #28a745, #20c997);
  padding: 15px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  color: #333;
  font-weight: 700;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.quick-actions {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.action-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Attendance Styles */
.attendance-content {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.attendance-content h2 {
  margin: 0 0 20px 0;
  color: #333;
}

.attendance-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.summary-label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #28a745;
}

.attendance-table {
  overflow-x: auto;
}

.attendance-table table {
  width: 100%;
  border-collapse: collapse;
}

.attendance-table th,
.attendance-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.attendance-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status.present {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status.weekend {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* Leave Styles */
.leave-content {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.leave-content h2 {
  margin: 0 0 30px 0;
  color: #333;
}

.leave-balance {
  margin-bottom: 40px;
}

.leave-balance h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.balance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.balance-card {
  padding: 20px;
  border-radius: 10px;
  background: #f8f9fa;
  border-left: 4px solid #28a745;
}

.balance-card h4 {
  margin: 0 0 10px 0;
  color: #333;
  text-transform: capitalize;
}

.balance-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.used {
  color: #dc3545;
}

.remaining {
  color: #28a745;
  font-weight: 600;
}

.balance-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.balance-fill {
  height: 100%;
  background: linear-gradient(135deg, #28a745, #20c997);
  transition: width 0.3s ease;
}

.leave-request h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.leave-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #28a745;
}

.submit-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Payroll Styles */
.payroll-content {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.payroll-content h2 {
  margin: 0 0 30px 0;
  color: #333;
}

.payroll-summary {
  margin-bottom: 40px;
}

.salary-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  border-left: 4px solid #28a745;
}

.salary-card h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.salary-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.salary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.salary-item:last-child {
  border-bottom: none;
}

.salary-item.total {
  font-weight: 700;
  font-size: 1.1rem;
  border-top: 2px solid #28a745;
  padding-top: 15px;
  margin-top: 10px;
}

.positive {
  color: #28a745;
  font-weight: 600;
}

.negative {
  color: #dc3545;
  font-weight: 600;
}

.payroll-history h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.payslip-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.payslip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.download-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.download-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* Profile Styles */
.profile-content {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-content h2 {
  margin: 0 0 30px 0;
  color: #333;
}

.profile-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 10px;
  border-left: 4px solid #28a745;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.profile-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.5rem;
}

.profile-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-weight: 600;
  color: #333;
}

.detail-item span {
  color: #666;
}

.edit-profile-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.edit-profile-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-nav {
    padding: 0 20px;
    flex-wrap: wrap;
  }

  .dashboard-content {
    padding: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .attendance-summary {
    flex-direction: column;
    gap: 15px;
  }

  .balance-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .payslip-item {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
