import React, { useState } from 'react';
import './AdminDashboard.css';

interface User {
  id: string;
  name: string;
  role: 'employee' | 'admin' | 'hr';
  email: string;
  department?: string;
  position?: string;
}

interface AdminDashboardProps {
  user: User;
  onLogout: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    role: 'employee' as 'employee' | 'admin' | 'hr',
    department: '',
    position: ''
  });

  // Sample data
  const systemStats = {
    totalUsers: 156,
    activeUsers: 142,
    departments: 8,
    pendingRequests: 12
  };

  const allUsers = [
    { id: 'emp001', name: '<PERSON>', role: 'employee', email: '<EMAIL>', department: 'Engineering', position: 'Software Developer', status: 'Active' },
    { id: 'emp002', name: '<PERSON>', role: 'employee', email: '<EMAIL>', department: 'Marketing', position: 'Marketing Specialist', status: 'Active' },
    { id: 'hr001', name: '<PERSON>', role: 'hr', email: '<EMAIL>', department: 'Human Resources', position: 'HR Manager', status: 'Active' },
    { id: 'emp003', name: 'David Brown', role: 'employee', email: '<EMAIL>', department: 'Sales', position: 'Sales Representative', status: 'Inactive' },
  ];

  const systemLogs = [
    { timestamp: '2024-01-15 14:30', action: 'User Login', user: '<EMAIL>', status: 'Success' },
    { timestamp: '2024-01-15 14:25', action: 'Password Reset', user: '<EMAIL>', status: 'Success' },
    { timestamp: '2024-01-15 14:20', action: 'Failed Login', user: '<EMAIL>', status: 'Failed' },
    { timestamp: '2024-01-15 14:15', action: 'User Created', user: 'admin001', status: 'Success' },
  ];

  const handleCreateUser = (e: React.FormEvent) => {
    e.preventDefault();
    alert(`User ${newUser.name} created successfully!`);
    setNewUser({ name: '', email: '', role: 'employee', department: '', position: '' });
  };

  const renderOverview = () => (
    <div className="admin-overview">
      <div className="welcome-section">
        <h2>Admin Dashboard 🛡️</h2>
        <p>System overview and management controls</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-info">
            <h3>{systemStats.totalUsers}</h3>
            <p>Total Users</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <h3>{systemStats.activeUsers}</h3>
            <p>Active Users</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">🏢</div>
          <div className="stat-info">
            <h3>{systemStats.departments}</h3>
            <p>Departments</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <h3>{systemStats.pendingRequests}</h3>
            <p>Pending Requests</p>
          </div>
        </div>
      </div>

      <div className="quick-actions">
        <h3>Quick Actions</h3>
        <div className="action-buttons">
          <button className="action-btn" onClick={() => setActiveTab('users')}>
            👥 Manage Users
          </button>
          <button className="action-btn" onClick={() => setActiveTab('reports')}>
            📊 View Reports
          </button>
          <button className="action-btn" onClick={() => setActiveTab('settings')}>
            ⚙️ System Settings
          </button>
          <button className="action-btn" onClick={() => setActiveTab('logs')}>
            📋 System Logs
          </button>
        </div>
      </div>

      <div className="recent-activity">
        <h3>Recent System Activity</h3>
        <div className="activity-list">
          {systemLogs.slice(0, 3).map((log, index) => (
            <div key={index} className="activity-item">
              <div className="activity-info">
                <span className="activity-action">{log.action}</span>
                <span className="activity-user">{log.user}</span>
              </div>
              <div className="activity-meta">
                <span className="activity-time">{log.timestamp}</span>
                <span className={`activity-status ${log.status.toLowerCase()}`}>
                  {log.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderUsers = () => (
    <div className="users-management">
      <h2>👥 User Management</h2>
      
      <div className="user-actions">
        <div className="create-user-section">
          <h3>Create New User</h3>
          <form onSubmit={handleCreateUser} className="create-user-form">
            <div className="form-row">
              <input
                type="text"
                placeholder="Full Name"
                value={newUser.name}
                onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                required
              />
              <input
                type="email"
                placeholder="Email Address"
                value={newUser.email}
                onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                required
              />
            </div>
            <div className="form-row">
              <select
                value={newUser.role}
                onChange={(e) => setNewUser({...newUser, role: e.target.value as 'employee' | 'admin' | 'hr'})}
              >
                <option value="employee">Employee</option>
                <option value="hr">HR</option>
                <option value="admin">Admin</option>
              </select>
              <input
                type="text"
                placeholder="Department"
                value={newUser.department}
                onChange={(e) => setNewUser({...newUser, department: e.target.value})}
                required
              />
            </div>
            <div className="form-row">
              <input
                type="text"
                placeholder="Position"
                value={newUser.position}
                onChange={(e) => setNewUser({...newUser, position: e.target.value})}
                required
              />
              <button type="submit" className="create-btn">Create User</button>
            </div>
          </form>
        </div>
      </div>

      <div className="users-table">
        <h3>All Users</h3>
        <div className="table-container">
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Department</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {allUsers.map((user) => (
                <tr key={user.id}>
                  <td>{user.id}</td>
                  <td>{user.name}</td>
                  <td>{user.email}</td>
                  <td>
                    <span className={`role-badge ${user.role}`}>
                      {user.role.toUpperCase()}
                    </span>
                  </td>
                  <td>{user.department}</td>
                  <td>
                    <span className={`status-badge ${user.status.toLowerCase()}`}>
                      {user.status}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons-small">
                      <button className="edit-btn">✏️</button>
                      <button className="delete-btn">🗑️</button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderReports = () => (
    <div className="reports-section">
      <h2>📊 Reports & Analytics</h2>
      
      <div className="reports-grid">
        <div className="report-card">
          <h3>📈 User Growth</h3>
          <div className="chart-placeholder">
            <div className="chart-bar" style={{height: '60%'}}></div>
            <div className="chart-bar" style={{height: '80%'}}></div>
            <div className="chart-bar" style={{height: '70%'}}></div>
            <div className="chart-bar" style={{height: '90%'}}></div>
            <div className="chart-bar" style={{height: '100%'}}></div>
          </div>
          <p>User registration trend over the last 5 months</p>
        </div>

        <div className="report-card">
          <h3>🏢 Department Distribution</h3>
          <div className="pie-chart-placeholder">
            <div className="pie-slice engineering"></div>
            <div className="pie-slice marketing"></div>
            <div className="pie-slice sales"></div>
            <div className="pie-slice hr"></div>
          </div>
          <div className="legend">
            <div className="legend-item"><span className="color engineering"></span>Engineering (40%)</div>
            <div className="legend-item"><span className="color marketing"></span>Marketing (25%)</div>
            <div className="legend-item"><span className="color sales"></span>Sales (20%)</div>
            <div className="legend-item"><span className="color hr"></span>HR (15%)</div>
          </div>
        </div>

        <div className="report-card">
          <h3>⚡ System Performance</h3>
          <div className="performance-metrics">
            <div className="metric">
              <span className="metric-label">Server Uptime:</span>
              <span className="metric-value">99.9%</span>
            </div>
            <div className="metric">
              <span className="metric-label">Response Time:</span>
              <span className="metric-value">120ms</span>
            </div>
            <div className="metric">
              <span className="metric-label">Active Sessions:</span>
              <span className="metric-value">142</span>
            </div>
            <div className="metric">
              <span className="metric-label">Database Size:</span>
              <span className="metric-value">2.4GB</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="settings-section">
      <h2>⚙️ System Settings</h2>
      
      <div className="settings-grid">
        <div className="setting-card">
          <h3>🔐 Security Settings</h3>
          <div className="setting-options">
            <div className="setting-item">
              <label>
                <input type="checkbox" defaultChecked />
                Enable Two-Factor Authentication
              </label>
            </div>
            <div className="setting-item">
              <label>
                <input type="checkbox" defaultChecked />
                Require Strong Passwords
              </label>
            </div>
            <div className="setting-item">
              <label>
                <input type="checkbox" />
                Enable Login Notifications
              </label>
            </div>
          </div>
        </div>

        <div className="setting-card">
          <h3>📧 Email Settings</h3>
          <div className="setting-options">
            <div className="setting-item">
              <label>SMTP Server:</label>
              <input type="text" defaultValue="smtp.company.com" />
            </div>
            <div className="setting-item">
              <label>Port:</label>
              <input type="number" defaultValue="587" />
            </div>
            <div className="setting-item">
              <label>
                <input type="checkbox" defaultChecked />
                Enable SSL/TLS
              </label>
            </div>
          </div>
        </div>

        <div className="setting-card">
          <h3>🎨 System Appearance</h3>
          <div className="setting-options">
            <div className="setting-item">
              <label>Theme:</label>
              <select defaultValue="light">
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
              </select>
            </div>
            <div className="setting-item">
              <label>Language:</label>
              <select defaultValue="en">
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="settings-actions">
        <button className="save-btn">💾 Save Settings</button>
        <button className="backup-btn">📦 Create Backup</button>
        <button className="reset-btn">🔄 Reset to Defaults</button>
      </div>
    </div>
  );

  const renderLogs = () => (
    <div className="logs-section">
      <h2>📋 System Logs</h2>
      
      <div className="logs-filters">
        <select defaultValue="all">
          <option value="all">All Actions</option>
          <option value="login">Login Events</option>
          <option value="user">User Management</option>
          <option value="system">System Events</option>
        </select>
        <input type="date" defaultValue="2024-01-15" />
        <button className="filter-btn">🔍 Filter</button>
      </div>

      <div className="logs-table">
        <table>
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>Action</th>
              <th>User</th>
              <th>Status</th>
              <th>Details</th>
            </tr>
          </thead>
          <tbody>
            {systemLogs.map((log, index) => (
              <tr key={index}>
                <td>{log.timestamp}</td>
                <td>{log.action}</td>
                <td>{log.user}</td>
                <td>
                  <span className={`status-badge ${log.status.toLowerCase()}`}>
                    {log.status}
                  </span>
                </td>
                <td>
                  <button className="details-btn">👁️ View</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'users': return renderUsers();
      case 'reports': return renderReports();
      case 'settings': return renderSettings();
      case 'logs': return renderLogs();
      default: return renderOverview();
    }
  };

  return (
    <div className="admin-dashboard">
      <header className="dashboard-header">
        <div className="header-left">
          <h1>🛡️ Admin Panel</h1>
          <span className="user-info">{user.name} - System Administrator</span>
        </div>
        <button className="logout-btn" onClick={onLogout}>
          🚪 Logout
        </button>
      </header>

      <nav className="dashboard-nav">
        {[
          { id: 'overview', label: '📊 Overview' },
          { id: 'users', label: '👥 Users' },
          { id: 'reports', label: '📈 Reports' },
          { id: 'settings', label: '⚙️ Settings' },
          { id: 'logs', label: '📋 Logs' }
        ].map(tab => (
          <button
            key={tab.id}
            className={`nav-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>
    </div>
  );
};

export default AdminDashboard;
