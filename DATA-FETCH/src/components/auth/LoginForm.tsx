import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import type { User } from '../../types/User';
import Button from '../ui/Button';
import Input from '../ui/Input';

interface LoginFormProps {
  onLogin?: (user: User) => void;
  onError?: (error: string) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onLogin, onError }) => {
  const navigate = useNavigate();
  const [loginMethod, setLoginMethod] = useState<'email' | 'mobile' | 'username'>('email');
  const [loginValue, setLoginValue] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Fetch data from your JSON file
      const response = await fetch('http://localhost:1240/Users');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const users = await response.json();
      
      if (!Array.isArray(users)) {
        throw new Error('Invalid data format received from server');
      }
      
      // Find user by User_id, email, or name
      const foundUser = users.find((user: User) => {
        const inputValue = loginValue.toLowerCase().trim();
        
        // Check if input matches User_id (convert to string for comparison)
        if (String(user.User_id) === inputValue) return true;
        
        // Check if input matches email
        if (user.email && user.email.toLowerCase() === inputValue) return true;
        
        // Check if input matches name
        if (user.name && user.name.toLowerCase() === inputValue) return true;
        
        return false;
      });

      if (foundUser && foundUser.Password === password) {
        // Store user info in localStorage for dashboard use
        localStorage.setItem('currentUser', JSON.stringify(foundUser));
        
        // Call onLogin callback if provided
        if (onLogin) {
          onLogin(foundUser);
        }
        
        // Navigate based on role
        switch (foundUser.role.toLowerCase()) {
          case 'admin':
            navigate('/admin');
            break;
          case 'hr':
            navigate('/hr');
            break;
          case 'employee':
            navigate('/employee');
            break;
          default:
            setError('Invalid user role');
            break;
        }
      } else {
        const errorMsg = 'Invalid credentials. Please check your User ID/Email and password.';
        setError(errorMsg);
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMsg = 'Unable to connect to server. Please make sure the JSON server is running on port 1240.';
      setError(errorMsg);
      if (onError) {
        onError(errorMsg);
      }
    }
    
    setIsLoading(false);
  };

  return (
    <div style={{
      width: '100%',
      maxWidth: '400px',
      background: 'white',
      borderRadius: '20px',
      padding: '2rem',
      boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
      border: '1px solid rgba(0,0,0,0.05)'
    }}>
      {/* Logo */}
      <div style={{
        textAlign: 'center',
        marginBottom: '2rem'
      }}>
        <div style={{
          width: '60px',
          height: '60px',
          background: 'linear-gradient(45deg, #4CAF50, #2E7D32)',
          borderRadius: '15px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          margin: '0 auto 1rem',
          fontSize: '1.5rem',
          color: 'white'
        }}>
          🏢
        </div>
        <h2 style={{
          color: '#333',
          fontSize: '1.5rem',
          margin: '0 0 0.5rem 0'
        }}>
          Welcome!
        </h2>
        <p style={{
          color: '#666',
          fontSize: '0.9rem',
          margin: 0
        }}>
          Sign in to continue
        </p>
      </div>

      <form onSubmit={handleLogin}>
        {/* Login Method Tabs */}
        <div style={{
          display: 'flex',
          marginBottom: '1.5rem',
          background: '#f5f5f5',
          borderRadius: '10px',
          padding: '4px'
        }}>
          {[
            { key: 'email' as const, label: 'Email' },
            { key: 'mobile' as const, label: 'Mobile' },
            { key: 'username' as const, label: 'Username' }
          ].map((method) => (
            <button
              key={method.key}
              type="button"
              onClick={() => setLoginMethod(method.key)}
              style={{
                flex: 1,
                padding: '0.5rem',
                border: 'none',
                borderRadius: '8px',
                background: loginMethod === method.key ? 'white' : 'transparent',
                color: loginMethod === method.key ? '#2d5a27' : '#666',
                fontSize: '0.9rem',
                fontWeight: loginMethod === method.key ? 'bold' : 'normal',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: loginMethod === method.key ? '0 2px 4px rgba(0,0,0,0.1)' : 'none'
              }}
            >
              {method.label}
            </button>
          ))}
        </div>

        {/* Login Input */}
        <div style={{ marginBottom: '1rem' }}>
          <label style={{
            display: 'block',
            marginBottom: '0.5rem',
            color: '#333',
            fontSize: '0.9rem',
            fontWeight: '500'
          }}>
            {loginMethod === 'email' ? 'EMAIL' : 
             loginMethod === 'mobile' ? 'MOBILE NO' : 'USERNAME'}
          </label>
          <Input
            type={loginMethod === 'email' ? 'email' : 'text'}
            value={loginValue}
            onChange={setLoginValue}
            placeholder={
              loginMethod === 'email' ? 'Enter your email' :
              loginMethod === 'mobile' ? 'Enter mobile number' :
              'Enter username'
            }
            required
          />
          <p style={{
            fontSize: '0.8rem',
            color: '#999',
            margin: '0.5rem 0 0 0'
          }}>
            {loginMethod === 'mobile' ? 'Please enter 10 digit mobile no' : 
             `Login by ${loginMethod} code`}
          </p>
        </div>

        {/* Password Input */}
        <div style={{ marginBottom: '1.5rem' }}>
          <label style={{
            display: 'block',
            marginBottom: '0.5rem',
            color: '#333',
            fontSize: '0.9rem',
            fontWeight: '500'
          }}>
            PASSWORD
          </label>
          <div style={{ position: 'relative' }}>
            <Input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={setPassword}
              placeholder="Enter your password"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={{
                position: 'absolute',
                right: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '1.2rem'
              }}
            >
              {showPassword ? '👁️' : '👁️‍🗨️'}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div style={{
            background: '#ffebee',
            color: '#c62828',
            padding: '0.75rem',
            borderRadius: '8px',
            marginBottom: '1rem',
            fontSize: '0.9rem',
            border: '1px solid #ffcdd2'
          }}>
            {error}
          </div>
        )}

        {/* Login Button */}
        <Button
          type="submit"
          variant="primary"
          size="large"
          loading={isLoading}
          fullWidth
          style={{ marginBottom: '1rem' }}
        >
          {isLoading ? 'Signing in...' : 'NEXT'}
        </Button>

        {/* Forgot Password */}
        <div style={{ textAlign: 'center' }}>
          <button
            type="button"
            onClick={() => alert('Forgot password feature coming soon!')}
            style={{
              background: 'none',
              border: 'none',
              color: '#4CAF50',
              fontSize: '0.9rem',
              cursor: 'pointer',
              textDecoration: 'underline'
            }}
          >
            Forgot Password?
          </button>
        </div>
      </form>

      {/* Footer */}
      <div style={{
        textAlign: 'center',
        marginTop: '2rem',
        fontSize: '0.8rem',
        color: '#999'
      }}>
        <p>Are you a BP user? <span style={{ color: '#4CAF50', cursor: 'pointer' }}>Login here</span></p>
        <p style={{ marginTop: '1rem' }}>© 2024 HROne All Rights Reserved</p>
        <p>Version 5.0.18</p>
      </div>
    </div>
  );
};

export default LoginForm;
