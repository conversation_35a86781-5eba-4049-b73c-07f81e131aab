import React, { useState } from 'react';
import './LoginPage.css';

interface User {
  id: string;
  name: string;
  role: 'employee' | 'admin' | 'hr';
  email: string;
  department?: string;
  position?: string;
}

interface LoginPageProps {
  onLogin: (userId: string, password: string) => Promise<boolean>;
  isLoading: boolean;
  demoUsers: User[];
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin, isLoading, demoUsers }) => {
  const [userId, setUserId] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showDemo, setShowDemo] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!userId.trim() || !password.trim()) {
      setError('Please enter both User ID and Password');
      return;
    }

    const success = await onLogin(userId.trim(), password.trim());
    if (!success) {
      setError('Invalid credentials. Please try again.');
    }
  };

  const handleDemoLogin = (demoUserId: string) => {
    setUserId(demoUserId);
    setPassword('password123');
    setError('');
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-background-pattern"></div>
      </div>
      
      <div className="login-content">
        <div className="login-card">
          <div className="login-header">
            <div className="company-logo">
              <div className="logo-icon">🏢</div>
              <h1>HRM Portal</h1>
            </div>
            <p className="login-subtitle">Human Resource Management System</p>
          </div>

          <form onSubmit={handleSubmit} className="login-form">
            <div className="form-group">
              <label htmlFor="userId">User ID</label>
              <input
                type="text"
                id="userId"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="Enter your User ID"
                disabled={isLoading}
                className={error ? 'error' : ''}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                disabled={isLoading}
                className={error ? 'error' : ''}
              />
            </div>

            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <button 
              type="submit" 
              className="login-button"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="loading-spinner"></span>
                  Signing In...
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          <div className="demo-section">
            <button 
              type="button"
              className="demo-toggle"
              onClick={() => setShowDemo(!showDemo)}
            >
              {showDemo ? 'Hide' : 'Show'} Demo Accounts
            </button>

            {showDemo && (
              <div className="demo-accounts">
                <h3>Demo Accounts (Password: password123)</h3>
                <div className="demo-users">
                  {demoUsers.map((user) => (
                    <div 
                      key={user.id} 
                      className={`demo-user ${user.role}`}
                      onClick={() => handleDemoLogin(user.id)}
                    >
                      <div className="demo-user-info">
                        <div className="demo-user-id">{user.id}</div>
                        <div className="demo-user-name">{user.name}</div>
                        <div className="demo-user-role">{user.role.toUpperCase()}</div>
                      </div>
                      <div className="demo-user-arrow">→</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="login-footer">
          <p>&copy; 2024 HRM Portal. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
