/* Header Styles */
.hrm-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

/* Logo Styles */
.header-logo-link {
  text-decoration: none;
  color: inherit;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--color-text);
  transition: color var(--transition-fast);
}

.header-logo:hover {
  color: var(--color-primary);
}

.header-logo-icon {
  width: 2rem;
  height: 2rem;
  color: var(--color-primary);
}

.header-logo-text {
  font-weight: 700;
}

/* Navigation Styles */
.header-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* User Menu Styles */
.header-user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.header-user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-user-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--color-text);
  line-height: 1.2;
}

.header-user-role {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  line-height: 1.2;
}

/* Mobile Menu Toggle */
.header-mobile-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  gap: 0.25rem;
}

.header-mobile-toggle-line {
  width: 1.25rem;
  height: 2px;
  background-color: var(--color-text);
  transition: all var(--transition-fast);
  transform-origin: center;
}

.header-mobile-toggle--open .header-mobile-toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(0.25rem, 0.25rem);
}

.header-mobile-toggle--open .header-mobile-toggle-line:nth-child(2) {
  opacity: 0;
}

.header-mobile-toggle--open .header-mobile-toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(0.25rem, -0.25rem);
}

/* Mobile Menu */
.header-mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.header-mobile-menu--open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.header-mobile-user {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.header-mobile-user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 var(--spacing-md);
  }

  .header-nav {
    display: none;
  }

  .header-mobile-toggle {
    display: flex;
  }

  .header-user-details {
    display: none;
  }
}

@media (max-width: 640px) {
  .header-container {
    padding: 0 var(--spacing-md);
    height: 3.5rem;
  }

  .header-logo-text {
    display: none;
  }

  .header-logo-icon {
    width: 1.75rem;
    height: 1.75rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .hrm-header {
    background-color: var(--color-surface);
    border-bottom-color: var(--color-border);
  }

  .header-mobile-menu {
    background-color: var(--color-surface);
    border-bottom-color: var(--color-border);
  }
}

/* Ensure content below header is not hidden */
body {
  padding-top: 4rem;
}

@media (max-width: 640px) {
  body {
    padding-top: 3.5rem;
  }
}
