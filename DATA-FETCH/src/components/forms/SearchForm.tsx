import React, { useState, useEffect } from 'react';
import { Button, Input } from '../ui';
import { useDebounce } from '../../hooks';
import { USER_ROLES, DEPARTMENT_OPTIONS } from '../../constants';
import { cn } from '../../utils';
import './SearchForm.css';

interface SearchFilters {
  search: string;
  role: string;
  department: string;
  status: string;
}

interface SearchFormProps {
  onFiltersChange: (filters: SearchFilters) => void;
  initialFilters?: Partial<SearchFilters>;
  className?: string;
}

const SearchForm: React.FC<SearchFormProps> = ({
  onFiltersChange,
  initialFilters = {},
  className,
}) => {
  const [filters, setFilters] = useState<SearchFilters>({
    search: initialFilters.search || '',
    role: initialFilters.role || '',
    department: initialFilters.department || '',
    status: initialFilters.status || '',
  });

  const [isExpanded, setIsExpanded] = useState(false);

  // Debounce search input to avoid too many API calls
  const debouncedSearch = useDebounce(filters.search, 300);

  // Update filters when debounced search changes
  useEffect(() => {
    onFiltersChange({
      ...filters,
      search: debouncedSearch,
    });
  }, [debouncedSearch, filters.role, filters.department, filters.status, onFiltersChange]);

  // Handle filter changes
  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // For non-search filters, update immediately
    if (key !== 'search') {
      onFiltersChange(newFilters);
    }
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters: SearchFilters = {
      search: '',
      role: '',
      department: '',
      status: '',
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  // Check if any filters are active
  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className={cn('search-form', className)}>
      {/* Search Input - Always Visible */}
      <div className="search-form-main">
        <div className="search-form-search">
          <Input
            type="text"
            placeholder="Search users by name, email, or ID..."
            value={filters.search}
            onChange={(value) => handleFilterChange('search', value)}
            className="search-form-search-input"
          />
          <div className="search-form-search-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
          </div>
        </div>

        {/* Filter Toggle Button */}
        <Button
          type="button"
          variant="secondary"
          onClick={() => setIsExpanded(!isExpanded)}
          className={cn('search-form-toggle', isExpanded && 'search-form-toggle--active')}
        >
          <svg className="search-form-toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
          </svg>
          Filters
          {hasActiveFilters && (
            <span className="search-form-active-indicator">
              {Object.values(filters).filter(v => v !== '').length}
            </span>
          )}
        </Button>
      </div>

      {/* Expandable Filters */}
      <div className={cn('search-form-filters', isExpanded && 'search-form-filters--expanded')}>
        <div className="search-form-filters-grid">
          {/* Role Filter */}
          <div className="search-form-filter">
            <label className="search-form-filter-label">Role</label>
            <select
              value={filters.role}
              onChange={(e) => handleFilterChange('role', e.target.value)}
              className="search-form-select"
            >
              <option value="">All Roles</option>
              <option value={USER_ROLES.ADMIN}>Admin</option>
              <option value={USER_ROLES.HR}>HR</option>
              <option value={USER_ROLES.EMPLOYEE}>Employee</option>
            </select>
          </div>

          {/* Department Filter */}
          <div className="search-form-filter">
            <label className="search-form-filter-label">Department</label>
            <select
              value={filters.department}
              onChange={(e) => handleFilterChange('department', e.target.value)}
              className="search-form-select"
            >
              <option value="">All Departments</option>
              {DEPARTMENT_OPTIONS.map((dept) => (
                <option key={dept.value} value={dept.value}>
                  {dept.label}
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div className="search-form-filter">
            <label className="search-form-filter-label">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="search-form-select"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          {/* Clear Filters Button */}
          <div className="search-form-filter search-form-filter--actions">
            <Button
              type="button"
              variant="secondary"
              onClick={clearFilters}
              disabled={!hasActiveFilters}
              className="search-form-clear"
            >
              Clear Filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchForm;
