/* Search Form Container */
.search-form {
  background: var(--color-white);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

/* Main Search Section */
.search-form-main {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.search-form-search {
  position: relative;
  flex: 1;
}

.search-form-search-input {
  padding-left: 2.5rem;
}

.search-form-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-text-secondary);
  pointer-events: none;
}

.search-form-search-icon svg {
  width: 100%;
  height: 100%;
  stroke-width: 2;
}

/* Filter Toggle Button */
.search-form-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
  white-space: nowrap;
}

.search-form-toggle-icon {
  width: 1rem;
  height: 1rem;
  stroke-width: 2;
}

.search-form-toggle--active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.search-form-active-indicator {
  background-color: var(--color-danger);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 50%;
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.search-form-toggle--active .search-form-active-indicator {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Expandable Filters Section */
.search-form-filters {
  border-top: 1px solid var(--color-border);
  background-color: var(--color-gray-50);
  max-height: 0;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.search-form-filters--expanded {
  max-height: 200px;
  padding: var(--spacing-lg);
}

.search-form-filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  align-items: end;
}

/* Individual Filter */
.search-form-filter {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.search-form-filter--actions {
  justify-content: flex-end;
}

.search-form-filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text);
}

/* Select Styles */
.search-form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--color-text);
  background-color: var(--color-white);
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2rem;
}

.search-form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.1);
}

/* Clear Button */
.search-form-clear {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-form-main {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .search-form-search {
    width: 100%;
  }
  
  .search-form-toggle {
    width: 100%;
    justify-content: center;
  }
  
  .search-form-filters-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .search-form-filters--expanded {
    max-height: 400px;
  }
}

@media (max-width: 640px) {
  .search-form-main {
    padding: var(--spacing-md);
  }
  
  .search-form-filters--expanded {
    padding: var(--spacing-md);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .search-form {
    background: var(--color-surface);
    border-color: var(--color-border);
  }
  
  .search-form-filters {
    background-color: var(--color-gray-800);
    border-top-color: var(--color-border);
  }
  
  .search-form-select {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  }
}

/* Animation for smooth expand/collapse */
@keyframes expand {
  from {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    max-height: 200px;
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
  }
}

@keyframes collapse {
  from {
    max-height: 200px;
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
  }
  to {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}

/* Focus States for Accessibility */
.search-form-select:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
