/* User Form Container */
.user-form-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.user-form-card {
  width: 100%;
}

.user-form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

/* Form Layout */
.user-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.user-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.user-form-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* Form Labels */
.user-form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
}

.user-form-required {
  color: var(--color-danger);
}

/* Select Styles */
.user-form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-white);
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.user-form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

.user-form-select--error {
  border-color: var(--color-danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.user-form-select:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

/* Error Messages */
.user-form-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-danger);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.user-form-submit-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-md);
  color: var(--color-danger);
  font-size: 0.875rem;
}

.user-form-error-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  stroke-width: 2;
}

/* Form Actions */
.user-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .user-form-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }
  
  .user-form-actions button {
    width: 100%;
  }
}

@media (max-width: 640px) {
  .user-form-container {
    padding: 0;
  }
  
  .user-form-card {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .user-form-select {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  }
  
  .user-form-select:disabled {
    background-color: var(--color-gray-800);
    color: var(--color-text-secondary);
  }
  
  .user-form-submit-error {
    background-color: #450a0a;
    border-color: #7f1d1d;
    color: #fca5a5;
  }
}

/* Focus States for Accessibility */
.user-form-select:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Loading State */
.user-form--loading {
  pointer-events: none;
  opacity: 0.7;
}

.user-form--loading .user-form-select,
.user-form--loading input {
  cursor: wait;
}

/* Success State */
.user-form-success {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: var(--radius-md);
  color: var(--color-success);
  font-size: 0.875rem;
}

.user-form-success-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  stroke-width: 2;
}
