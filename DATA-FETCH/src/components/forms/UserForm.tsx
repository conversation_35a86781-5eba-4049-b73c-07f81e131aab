import React, { useState } from 'react';
import { User } from '../../types';
import { useForm } from '../../hooks';
import { Button, Input, Card, CardHeader, CardBody } from '../ui';
import { validateRequired, isValidEmail } from '../../utils';
import { DEPARTMENT_OPTIONS, USER_ROLES } from '../../constants';
import './UserForm.css';

interface UserFormData {
  name: string;
  email: string;
  role: User['role'];
  department: string;
  password: string;
  confirmPassword: string;
}

interface UserFormProps {
  user?: User;
  onSubmit: (userData: Omit<User, 'User_id'>) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  title?: string;
}

const UserForm: React.FC<UserFormProps> = ({
  user,
  onSubmit,
  onCancel,
  isLoading = false,
  title = user ? 'Edit User' : 'Create New User',
}) => {
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Form validation rules
  const validationRules = {
    name: validateRequired,
    email: (value: string) => {
      const requiredError = validateRequired(value);
      if (requiredError) return requiredError;
      return isValidEmail(value) ? null : 'Please enter a valid email address';
    },
    role: validateRequired,
    department: validateRequired,
    password: user ? undefined : (value: string) => {
      const requiredError = validateRequired(value);
      if (requiredError) return requiredError;
      return value.length >= 8 ? null : 'Password must be at least 8 characters long';
    },
    confirmPassword: user ? undefined : (value: string) => {
      const requiredError = validateRequired(value);
      if (requiredError) return requiredError;
      return value === values.password ? null : 'Passwords do not match';
    },
  };

  // Initialize form with user data or defaults
  const initialValues: UserFormData = {
    name: user?.name || '',
    email: user?.email || '',
    role: user?.role || 'Employee',
    department: user?.department || '',
    password: '',
    confirmPassword: '',
  };

  const {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    handleSubmit,
    reset,
  } = useForm<UserFormData>(initialValues, validationRules);

  const onFormSubmit = async (formData: UserFormData) => {
    setSubmitError(null);
    
    try {
      const userData: Omit<User, 'User_id'> = {
        name: formData.name,
        email: formData.email,
        role: formData.role,
        department: formData.department,
        Password: formData.password || user?.Password || '',
        status: user?.status || 'active',
      };

      await onSubmit(userData);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'An error occurred');
    }
  };

  return (
    <div className="user-form-container">
      <Card className="user-form-card">
        <CardHeader>
          <h2 className="user-form-title">{title}</h2>
        </CardHeader>
        <CardBody>
          <form onSubmit={handleSubmit(onFormSubmit)} className="user-form">
            <div className="user-form-grid">
              {/* Name Field */}
              <div className="user-form-field">
                <Input
                  label="Full Name"
                  type="text"
                  placeholder="Enter full name"
                  value={values.name}
                  onChange={(value) => setValue('name', value)}
                  error={touched.name ? errors.name : undefined}
                  disabled={isLoading}
                  required
                  onBlur={() => setFieldTouched('name')}
                />
              </div>

              {/* Email Field */}
              <div className="user-form-field">
                <Input
                  label="Email Address"
                  type="email"
                  placeholder="Enter email address"
                  value={values.email}
                  onChange={(value) => setValue('email', value)}
                  error={touched.email ? errors.email : undefined}
                  disabled={isLoading}
                  required
                  onBlur={() => setFieldTouched('email')}
                />
              </div>

              {/* Role Field */}
              <div className="user-form-field">
                <label className="user-form-label">
                  Role <span className="user-form-required">*</span>
                </label>
                <select
                  value={values.role}
                  onChange={(e) => setValue('role', e.target.value as User['role'])}
                  onBlur={() => setFieldTouched('role')}
                  disabled={isLoading}
                  className={`user-form-select ${touched.role && errors.role ? 'user-form-select--error' : ''}`}
                >
                  <option value="">Select a role</option>
                  <option value={USER_ROLES.ADMIN}>Admin</option>
                  <option value={USER_ROLES.HR}>HR</option>
                  <option value={USER_ROLES.EMPLOYEE}>Employee</option>
                </select>
                {touched.role && errors.role && (
                  <div className="user-form-error">{errors.role}</div>
                )}
              </div>

              {/* Department Field */}
              <div className="user-form-field">
                <label className="user-form-label">
                  Department <span className="user-form-required">*</span>
                </label>
                <select
                  value={values.department}
                  onChange={(e) => setValue('department', e.target.value)}
                  onBlur={() => setFieldTouched('department')}
                  disabled={isLoading}
                  className={`user-form-select ${touched.department && errors.department ? 'user-form-select--error' : ''}`}
                >
                  <option value="">Select a department</option>
                  {DEPARTMENT_OPTIONS.map((dept) => (
                    <option key={dept.value} value={dept.value}>
                      {dept.label}
                    </option>
                  ))}
                </select>
                {touched.department && errors.department && (
                  <div className="user-form-error">{errors.department}</div>
                )}
              </div>

              {/* Password Fields (only for new users or password change) */}
              {!user && (
                <>
                  <div className="user-form-field">
                    <Input
                      label="Password"
                      type="password"
                      placeholder="Enter password"
                      value={values.password}
                      onChange={(value) => setValue('password', value)}
                      error={touched.password ? errors.password : undefined}
                      disabled={isLoading}
                      required
                      onBlur={() => setFieldTouched('password')}
                    />
                  </div>

                  <div className="user-form-field">
                    <Input
                      label="Confirm Password"
                      type="password"
                      placeholder="Confirm password"
                      value={values.confirmPassword}
                      onChange={(value) => setValue('confirmPassword', value)}
                      error={touched.confirmPassword ? errors.confirmPassword : undefined}
                      disabled={isLoading}
                      required
                      onBlur={() => setFieldTouched('confirmPassword')}
                    />
                  </div>
                </>
              )}
            </div>

            {/* Submit Error */}
            {submitError && (
              <div className="user-form-submit-error">
                <svg className="user-form-error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="12" y1="8" x2="12" y2="12"/>
                  <line x1="12" y1="16" x2="12.01" y2="16"/>
                </svg>
                <span>{submitError}</span>
              </div>
            )}

            {/* Form Actions */}
            <div className="user-form-actions">
              {onCancel && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                loading={isLoading}
                disabled={isLoading}
              >
                {user ? 'Update User' : 'Create User'}
              </Button>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
};

export default UserForm;
