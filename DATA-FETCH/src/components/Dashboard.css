/* Dashboard Container */
.dashboard {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.dashboard-welcome {
  flex: 1;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: var(--spacing-sm);
}

.dashboard-subtitle {
  font-size: 1rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.dashboard-date {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  background: var(--color-gray-50);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

/* Stats Grid */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* Charts Section */
.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
}

.dashboard-chart-item {
  min-height: 300px;
}

/* Activity Section */
.dashboard-activity {
  width: 100%;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.activity-item:hover {
  background-color: var(--color-gray-50);
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon svg {
  width: 1.25rem;
  height: 1.25rem;
  stroke-width: 2;
}

.activity-icon--login {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.activity-icon--logout {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
}

.activity-icon--update {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

.activity-icon--create {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--color-info);
}

.activity-icon--delete {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 0.875rem;
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
}

.activity-time {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

/* Loading States */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-lg);
}

.dashboard-loading-spinner {
  position: relative;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-loading p {
  color: var(--color-text-secondary);
  font-size: 1rem;
}

/* Error State */
.dashboard-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--color-danger);
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-charts {
    grid-template-columns: 1fr;
  }
  
  .dashboard-chart-item {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: var(--spacing-md);
    gap: var(--spacing-lg);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .dashboard-title {
    font-size: 1.75rem;
  }
  
  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .dashboard-charts {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .activity-item {
    padding: var(--spacing-sm);
  }
  
  .activity-icon {
    width: 2rem;
    height: 2rem;
  }
  
  .activity-icon svg {
    width: 1rem;
    height: 1rem;
  }
}

@media (max-width: 640px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
  
  .activity-list {
    max-height: 300px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dashboard-date {
    background: var(--color-gray-800);
    border-color: var(--color-border);
  }
  
  .activity-item:hover {
    background-color: var(--color-gray-800);
  }
  
  .activity-icon--login {
    background-color: rgba(16, 185, 129, 0.2);
  }
  
  .activity-icon--logout {
    background-color: rgba(239, 68, 68, 0.2);
  }
  
  .activity-icon--update {
    background-color: rgba(245, 158, 11, 0.2);
  }
  
  .activity-icon--create {
    background-color: rgba(59, 130, 246, 0.2);
  }
  
  .activity-icon--delete {
    background-color: rgba(239, 68, 68, 0.2);
  }
}
