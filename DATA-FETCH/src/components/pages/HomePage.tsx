import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    { icon: '👥', title: 'Employee Management', desc: 'Complete employee lifecycle management' },
    { icon: '📊', title: 'Analytics & Reports', desc: 'Real-time insights and detailed reports' },
    { icon: '💰', title: 'Payroll Management', desc: 'Automated payroll processing' },
    { icon: '📅', title: 'Leave Management', desc: 'Easy leave tracking and approval' },
    { icon: '🎯', title: 'Performance Tracking', desc: 'Goal setting and performance reviews' },
    { icon: '🔒', title: 'Secure & Compliant', desc: 'Enterprise-grade security' }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%)',
      display: 'flex',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Left Side - Hero Section */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2rem',
        color: 'white',
        textAlign: 'center'
      }}>
        {/* Logo */}
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '20px',
          padding: '1rem',
          marginBottom: '2rem',
          backdropFilter: 'blur(10px)'
        }}>
          <div style={{
            width: '80px',
            height: '80px',
            background: 'linear-gradient(45deg, #4CAF50, #2E7D32)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '2rem',
            fontWeight: 'bold',
            color: 'white'
          }}>
            HR
          </div>
        </div>

        <h1 style={{
          fontSize: '3.5rem',
          fontWeight: '300',
          marginBottom: '1rem',
          letterSpacing: '-2px'
        }}>
          Meet HRM Portal
        </h1>
        
        <h2 style={{
          fontSize: '2rem',
          fontWeight: '400',
          marginBottom: '2rem',
          color: '#a8d5a8'
        }}>
          Your Everyday HR Companion
        </h2>

        <p style={{
          fontSize: '1.2rem',
          marginBottom: '3rem',
          opacity: 0.9,
          maxWidth: '500px',
          lineHeight: '1.6'
        }}>
          Streamline your HR processes with our intelligent platform. 
          Manage employees, track performance, and boost productivity.
        </p>

        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem',
          width: '100%',
          maxWidth: '300px'
        }}>
          <Button
            onClick={() => navigate('/login')}
            variant="warning"
            size="large"
            fullWidth
            style={{
              borderRadius: '50px',
              textTransform: 'uppercase',
              letterSpacing: '1px'
            }}
          >
            HURRY UP! LOG IN
          </Button>

          <Button
            onClick={() => navigate('/demo')}
            variant="secondary"
            size="large"
            fullWidth
            style={{
              borderRadius: '50px',
              textTransform: 'uppercase',
              letterSpacing: '1px',
              background: 'transparent',
              color: '#FFD700',
              border: '2px solid #FFD700'
            }}
          >
            START USING NOW
          </Button>
        </div>

        {/* AI Assistant Icon */}
        <div style={{
          marginTop: '3rem',
          position: 'relative'
        }}>
          <div style={{
            width: '100px',
            height: '100px',
            background: 'linear-gradient(45deg, #FF6B6B, #FF8E53)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '2.5rem',
            animation: 'float 3s ease-in-out infinite'
          }}>
            🤖
          </div>
          <div style={{
            position: 'absolute',
            top: '-10px',
            right: '-10px',
            background: '#FFD700',
            borderRadius: '50%',
            width: '30px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1rem'
          }}>
            ⭐
          </div>
        </div>

        <p style={{
          marginTop: '1rem',
          fontSize: '1rem',
          opacity: 0.8
        }}>
          Just Ask - chat or speak, AI Does it.
        </p>
      </div>

      {/* Right Side - Features */}
      <div style={{
        flex: 1,
        background: 'rgba(255,255,255,0.05)',
        backdropFilter: 'blur(10px)',
        padding: '2rem',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '20px',
          padding: '2rem',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255,255,255,0.2)'
        }}>
          <h3 style={{
            color: 'white',
            fontSize: '1.5rem',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            🚀 Key Features
          </h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {features.map((feature, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                padding: '1rem',
                background: 'rgba(255,255,255,0.05)',
                borderRadius: '10px',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
                e.currentTarget.style.transform = 'translateX(10px)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.background = 'rgba(255,255,255,0.05)';
                e.currentTarget.style.transform = 'translateX(0)';
              }}>
                <div style={{
                  fontSize: '2rem',
                  width: '50px',
                  textAlign: 'center'
                }}>
                  {feature.icon}
                </div>
                <div>
                  <h4 style={{
                    color: 'white',
                    margin: '0 0 0.5rem 0',
                    fontSize: '1.1rem'
                  }}>
                    {feature.title}
                  </h4>
                  <p style={{
                    color: 'rgba(255,255,255,0.7)',
                    margin: 0,
                    fontSize: '0.9rem'
                  }}>
                    {feature.desc}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Demo Credentials Section */}
      <div style={{
        position: 'absolute',
        bottom: '2rem',
        left: '2rem',
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '15px',
        padding: '1.5rem',
        backdropFilter: 'blur(10px)',
        maxWidth: '400px'
      }}>
        <h3 style={{ marginBottom: '1rem', color: '#FFD700' }}>Demo Credentials</h3>
        <div style={{ textAlign: 'left', fontSize: '0.9rem', lineHeight: '1.6', color: 'white' }}>
          <p><strong>Admin:</strong> 3000 / Akaash@123</p>
          <p><strong>HR:</strong> 2000 / Akaash@123</p>
          <p><strong>Employee:</strong> 226170 / Akaash@123</p>
          <p><strong>By Email:</strong> <EMAIL></p>
          <p><strong>By Name:</strong> Jayaprakash V</p>
        </div>
      </div>

      {/* Floating Animation CSS */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        
        @media (max-width: 768px) {
          .home-container {
            flex-direction: column !important;
          }
        }
      `}</style>
    </div>
  );
};

export default HomePage;
