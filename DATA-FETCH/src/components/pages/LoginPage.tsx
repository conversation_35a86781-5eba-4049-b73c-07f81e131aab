import React from 'react';
import { useNavigate } from 'react-router-dom';
import LoginForm from '../auth/LoginForm';
import type { User } from '../../types/User';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();

  const handleLoginSuccess = (user: User) => {
    console.log('Login successful:', user);
    // Additional login success logic can be added here
  };

  const handleLoginError = (error: string) => {
    console.error('Login error:', error);
    // Additional error handling can be added here
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%)',
      display: 'flex',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Left Side - Branding */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2rem',
        color: 'white',
        textAlign: 'center'
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '20px',
          padding: '1rem',
          marginBottom: '2rem',
          backdropFilter: 'blur(10px)'
        }}>
          <div style={{
            width: '80px',
            height: '80px',
            background: 'linear-gradient(45deg, #4CAF50, #2E7D32)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '2rem',
            fontWeight: 'bold',
            color: 'white'
          }}>
            HR
          </div>
        </div>

        <h1 style={{
          fontSize: '2.5rem',
          fontWeight: '300',
          marginBottom: '1rem'
        }}>
          Welcome Back!
        </h1>
        
        <p style={{
          fontSize: '1.2rem',
          opacity: 0.9,
          marginBottom: '2rem'
        }}>
          Sign in to access your HRM dashboard
        </p>

        <div style={{
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '15px',
          padding: '1.5rem',
          backdropFilter: 'blur(10px)',
          maxWidth: '400px'
        }}>
          <h3 style={{ marginBottom: '1rem', color: '#FFD700' }}>Demo Credentials</h3>
          <div style={{ textAlign: 'left', fontSize: '0.9rem', lineHeight: '1.6' }}>
            <p><strong>Admin:</strong> 3000 / Akaash@123</p>
            <p><strong>HR:</strong> 2000 / Akaash@123</p>
            <p><strong>Employee:</strong> 226170 / Akaash@123</p>
            <p><strong>By Email:</strong> <EMAIL></p>
            <p><strong>By Name:</strong> Jayaprakash V</p>
          </div>
        </div>

        {/* Back to Home Button */}
        <button
          onClick={() => navigate('/')}
          style={{
            marginTop: '2rem',
            background: 'transparent',
            border: '2px solid rgba(255,255,255,0.3)',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '25px',
            cursor: 'pointer',
            fontSize: '0.9rem',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            (e.target as HTMLButtonElement).style.background = 'rgba(255,255,255,0.1)';
            (e.target as HTMLButtonElement).style.borderColor = 'rgba(255,255,255,0.5)';
          }}
          onMouseOut={(e) => {
            (e.target as HTMLButtonElement).style.background = 'transparent';
            (e.target as HTMLButtonElement).style.borderColor = 'rgba(255,255,255,0.3)';
          }}
        >
          ← Back to Home
        </button>
      </div>

      {/* Right Side - Login Form */}
      <div style={{
        flex: 1,
        background: 'rgba(255,255,255,0.95)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2rem'
      }}>
        <LoginForm 
          onLogin={handleLoginSuccess}
          onError={handleLoginError}
        />
      </div>

      {/* Responsive Design */}
      <style>{`
        @media (max-width: 768px) {
          .login-container {
            flex-direction: column !important;
          }
          
          .login-left {
            min-height: 40vh !important;
            padding: 1rem !important;
          }
          
          .login-right {
            padding: 1rem !important;
          }
        }
      `}</style>
    </div>
  );
};

export default LoginPage;
