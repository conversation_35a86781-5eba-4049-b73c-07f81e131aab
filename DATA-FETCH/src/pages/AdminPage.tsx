import React from 'react';
import DashboardLayout from '../components/layout/DashboardLayout';
const adminNavItems = [ { path: '/admin/dashboard', name: 'Dashboard', icon: '📊' }, { path: '/admin/users', name: 'User Management', icon: '👥' }, { path: '/admin/settings', name: '<PERSON><PERSON><PERSON>', icon: '⚙️' } ];
const AdminPage: React.FC = () => <DashboardLayout navItems={adminNavItems}><h1>Admin Dashboard</h1></DashboardLayout>;
export default AdminPage;