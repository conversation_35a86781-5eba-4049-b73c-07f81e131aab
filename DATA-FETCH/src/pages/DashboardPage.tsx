import React from 'react';
import Header from '../components/Header';
import Dashboard from '../components/Dashboard';
import { useAuth } from '../context';

const DashboardPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return (
      <div className="dashboard-page">
        <Header />
        <main className="dashboard-main">
          <div className="dashboard-unauthorized">
            <h1>Access Denied</h1>
            <p>You need to be logged in to view this page.</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="dashboard-page">
      <Header />
      <main className="dashboard-main">
        <Dashboard />
      </main>
    </div>
  );
};

export default DashboardPage;
