import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useForm } from '../hooks';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Card, { CardBody } from '../components/ui/Card';
import { validateRequired } from '../utils';
import './LoginPage.css';

interface LoginFormData {
  userId: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Form handling with validation
  const {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    handleSubmit,
    reset: _reset,
  } = useForm<LoginFormData>(
    { userId: '', password: '' },
    {
      userId: validateRequired,
      password: validateRequired,
    }
  );

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Clear errors when component mounts or form values change
  useEffect(() => {
    clearError();
    setSubmitError(null);
  }, [values.userId, values.password, clearError]);

  const onSubmit = async (formData: LoginFormData) => {
    setSubmitError(null);

    try {
      const result = await login({
        userId: formData.userId,
        password: formData.password,
      });

      if (result.success && result.user) {
        // Navigate based on user role
        switch (result.user.role) {
          case 'Admin':
            navigate('/admin');
            break;
          case 'HR':
            navigate('/hr');
            break;
          case 'Employee':
            navigate('/employee');
            break;
          default:
            setSubmitError('Invalid user role detected.');
            break;
        }
      } else {
        setSubmitError(result.message);
      }
    } catch (err) {
      setSubmitError('An unexpected error occurred. Please try again.');
    }
  };

  return (
    <div className="login-page">
      <div className="login-background">
        <div className="login-background-pattern"></div>
      </div>

      <div className="login-container">
        <Card className="login-card" shadow="large">
          <CardBody>
            {/* Logo and Header */}
            <div className="login-header">
              <div className="login-logo">
                <svg className="login-logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                  <path d="M2 17l10 5 10-5"/>
                  <path d="M2 12l10 5 10-5"/>
                </svg>
              </div>
              <h1 className="login-title">Welcome Back</h1>
              <p className="login-subtitle">
                Sign in to your HRM Portal account
              </p>
            </div>

            {/* Login Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="login-form">
              <div className="login-form-fields">
                <Input
                  label="User ID"
                  type="text"
                  placeholder="Enter your user ID"
                  value={values.userId}
                  onChange={(value) => setValue('userId', value)}
                  error={touched.userId ? errors.userId : undefined}
                  disabled={isLoading}
                  required
                  onBlur={() => setFieldTouched('userId')}
                />

                <Input
                  label="Password"
                  type="password"
                  placeholder="Enter your password"
                  value={values.password}
                  onChange={(value) => setValue('password', value)}
                  error={touched.password ? errors.password : undefined}
                  disabled={isLoading}
                  required
                  onBlur={() => setFieldTouched('password')}
                />
              </div>

              {/* Error Messages */}
              {(error || submitError) && (
                <div className="login-error">
                  <svg className="login-error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="12" y1="8" x2="12" y2="12"/>
                    <line x1="12" y1="16" x2="12.01" y2="16"/>
                  </svg>
                  <span>{error || submitError}</span>
                </div>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                variant="primary"
                size="large"
                loading={isLoading}
                disabled={isLoading}
                className="login-submit-btn"
              >
                {isLoading ? 'Signing In...' : 'Sign In'}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="login-demo">
              <p className="login-demo-title">Demo Credentials:</p>
              <div className="login-demo-credentials">
                <div className="login-demo-item">
                  <strong>Admin:</strong> 226170 / Akaash@123
                </div>
                <div className="login-demo-item">
                  <strong>Employee:</strong> 226171 / Akaash@123
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;