import React from 'react';
import DashboardLayout from '../components/layout/DashboardLayout';
const employeeNavItems = [ { path: '/employee/profile', name: 'My Profile', icon: '👤' }, { path: '/employee/leave', name: 'Request Leave', icon: '🗓️' }, { path: '/employee/payslips', name: 'My Payslips', icon: '💰' } ];
const EmployeePage: React.FC = () => <DashboardLayout navItems={employeeNavItems}><h1>Employee Portal</h1></DashboardLayout>;
export default EmployeePage;