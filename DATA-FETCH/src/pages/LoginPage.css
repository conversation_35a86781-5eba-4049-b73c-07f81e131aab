/* Login Page Styles */
.login-page {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: var(--spacing-lg);
  overflow: hidden;
}

/* Animated Background */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
}

.login-background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: float 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Login Container */
.login-container {
  width: 100%;
  max-width: 420px;
  z-index: 1;
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Login Header */
.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.login-logo {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.login-logo-icon {
  width: 3rem;
  height: 3rem;
  color: var(--color-primary);
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: var(--spacing-sm);
}

.login-subtitle {
  color: var(--color-text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Login Form */
.login-form {
  width: 100%;
}

.login-form-fields {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

/* Error Message */
.login-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-md);
  color: #dc2626;
  font-size: 0.875rem;
  margin-bottom: var(--spacing-lg);
}

.login-error-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  stroke-width: 2;
}

/* Submit Button */
.login-submit-btn {
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

/* Demo Credentials */
.login-demo {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.login-demo-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.login-demo-credentials {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.login-demo-item {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  font-family: 'Courier New', monospace;
  background-color: var(--color-gray-50);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

/* Responsive Design */
@media (max-width: 640px) {
  .login-page {
    padding: var(--spacing-md);
  }

  .login-container {
    max-width: none;
  }

  .login-title {
    font-size: 1.75rem;
  }

  .login-demo-credentials {
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .login-page {
    padding: var(--spacing-sm);
  }

  .login-title {
    font-size: 1.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .login-background {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }

  .login-card {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .login-error {
    background-color: #450a0a;
    border-color: #7f1d1d;
    color: #fca5a5;
  }

  .login-demo-item {
    background-color: var(--color-gray-800);
    color: var(--color-gray-300);
  }
}