import React from 'react';
import Header from '../components/Header';
import './HomePage.css';

const HomePage: React.FC = () => {
  return (
    <div className="home-page-container">
      <Header />
      <main className="home-main">
        <div className="hero-section">
          <div className="hero-content">
            <h1 className="hero-title">Welcome to HRM Portal</h1>
            <p className="hero-subtitle">
              Manage your human resources efficiently with our modern, interactive platform.
            </p>
            <div className="hero-features">
              <div className="feature-card">
                <div className="feature-icon">👥</div>
                <h3>User Management</h3>
                <p>Comprehensive user management with role-based access control</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">📊</div>
                <h3>Analytics Dashboard</h3>
                <p>Real-time insights and interactive data visualizations</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">🔒</div>
                <h3>Secure & Reliable</h3>
                <p>Enterprise-grade security with modern web standards</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default HomePage;