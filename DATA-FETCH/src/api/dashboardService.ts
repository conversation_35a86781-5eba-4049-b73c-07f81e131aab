import type { DashboardStats, ActivityItem, ApiResponse } from '../types';
// import { apiClient } from './client';
import { getUserStats } from './userService';

// Get dashboard statistics
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  try {
    // Get user statistics
    const userStatsResponse = await getUserStats();
    
    if (!userStatsResponse.success || !userStatsResponse.data) {
      throw new Error('Failed to get user statistics');
    }
    
    // Generate mock recent activity
    const recentActivity: ActivityItem[] = [
      {
        id: '1',
        user: 'Jayaprakash V',
        action: 'logged in',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        type: 'login',
      },
      {
        id: '2',
        user: 'Akaash S',
        action: 'updated profile',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        type: 'update',
      },
      {
        id: '3',
        user: 'Balaganesh S',
        action: 'logged out',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        type: 'logout',
      },
      {
        id: '4',
        user: 'Admin',
        action: 'created new user',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        type: 'create',
      },
      {
        id: '5',
        user: 'Yuvesh Babu',
        action: 'logged in',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        type: 'login',
      },
    ];
    
    const dashboardStats: DashboardStats = {
      totalUsers: userStatsResponse.data.totalUsers,
      activeUsers: userStatsResponse.data.activeUsers,
      totalDepartments: Object.keys(userStatsResponse.data.usersByRole).length,
      recentActivity,
    };
    
    return {
      success: true,
      data: dashboardStats,
    };
  } catch (error) {
    console.error('Get Dashboard Stats Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get dashboard statistics',
    };
  }
};

// Get recent activity with pagination
export const getRecentActivity = async (params?: {
  page?: number;
  limit?: number;
  type?: ActivityItem['type'];
}): Promise<ApiResponse<{
  activities: ActivityItem[];
  total: number;
  page: number;
  totalPages: number;
}>> => {
  try {
    // Generate mock activity data
    const allActivities: ActivityItem[] = [
      {
        id: '1',
        user: 'Jayaprakash V',
        action: 'logged in',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        type: 'login',
      },
      {
        id: '2',
        user: 'Akaash S',
        action: 'updated profile',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        type: 'update',
      },
      {
        id: '3',
        user: 'Balaganesh S',
        action: 'logged out',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        type: 'logout',
      },
      {
        id: '4',
        user: 'Admin',
        action: 'created new user',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        type: 'create',
      },
      {
        id: '5',
        user: 'Yuvesh Babu',
        action: 'logged in',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        type: 'login',
      },
      {
        id: '6',
        user: 'Poneaswaran',
        action: 'updated password',
        timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
        type: 'update',
      },
      {
        id: '7',
        user: 'Gugan',
        action: 'logged out',
        timestamp: new Date(Date.now() - 120 * 60 * 1000).toISOString(),
        type: 'logout',
      },
      {
        id: '8',
        user: 'Thotacharan',
        action: 'logged in',
        timestamp: new Date(Date.now() - 150 * 60 * 1000).toISOString(),
        type: 'login',
      },
    ];
    
    // Apply filtering
    let filteredActivities = allActivities;
    if (params?.type) {
      filteredActivities = allActivities.filter(activity => activity.type === params.type);
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedActivities = filteredActivities.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: {
        activities: paginatedActivities,
        total: filteredActivities.length,
        page,
        totalPages: Math.ceil(filteredActivities.length / limit),
      },
    };
  } catch (error) {
    console.error('Get Recent Activity Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get recent activity',
    };
  }
};

// Get system health status
export const getSystemHealth = async (): Promise<ApiResponse<{
  status: 'healthy' | 'warning' | 'error';
  uptime: number;
  lastBackup: string;
  activeConnections: number;
}>> => {
  try {
    // Mock system health data
    const healthData = {
      status: 'healthy' as const,
      uptime: Math.floor(Math.random() * 86400), // Random uptime in seconds
      lastBackup: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      activeConnections: Math.floor(Math.random() * 50) + 10,
    };
    
    return {
      success: true,
      data: healthData,
    };
  } catch (error) {
    console.error('Get System Health Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get system health',
    };
  }
};
