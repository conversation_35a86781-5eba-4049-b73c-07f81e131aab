import type { User, ApiResponse, PaginatedResponse } from '../types';
import { API_CONFIG } from '../constants';
import { apiClient } from './client';

// Get all users with optional filtering and pagination
export const getUsers = async (params?: {
  page?: number;
  limit?: number;
  role?: string;
  search?: string;
}): Promise<PaginatedResponse<User>> => {
  try {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.role) queryParams.append('role', params.role);
    if (params?.search) queryParams.append('search', params.search);

    // const endpoint = `${API_CONFIG.ENDPOINTS.USERS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    // For now, we'll simulate pagination since the dummy API doesn't support it
    const users: User[] = await apiClient.get<User[]>(API_CONFIG.ENDPOINTS.USERS);
    
    // Apply client-side filtering
    let filteredUsers = users;
    
    if (params?.role) {
      filteredUsers = filteredUsers.filter(user => user.role === params.role);
    }
    
    if (params?.search) {
      const searchLower = params.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        String(user.User_id).toLowerCase().includes(searchLower)
      );
    }
    
    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      data: paginatedUsers,
      total: filteredUsers.length,
      page,
      limit,
      totalPages: Math.ceil(filteredUsers.length / limit),
    };
  } catch (error) {
    console.error('Get Users Error:', error);
    throw error;
  }
};

// Get user by ID
export const getUserById = async (userId: string | number): Promise<ApiResponse<User>> => {
  try {
    const users: User[] = await apiClient.get<User[]>(API_CONFIG.ENDPOINTS.USERS);
    const user = users.find(u => String(u.User_id) === String(userId));
    
    if (!user) {
      return {
        success: false,
        error: 'User not found',
      };
    }
    
    return {
      success: true,
      data: user,
    };
  } catch (error) {
    console.error('Get User By ID Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user',
    };
  }
};

// Create new user
export const createUser = async (userData: Omit<User, 'User_id'>): Promise<ApiResponse<User>> => {
  try {
    // In a real app, this would make a POST request
    // const newUser = await apiClient.post<User>(API_CONFIG.ENDPOINTS.USERS, userData);
    
    // For demo purposes, we'll simulate creating a user
    const newUser: User = {
      ...userData,
      User_id: Date.now(), // Generate a simple ID
    };
    
    return {
      success: true,
      data: newUser,
      message: 'User created successfully',
    };
  } catch (error) {
    console.error('Create User Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create user',
    };
  }
};

// Update user
export const updateUser = async (
  userId: string | number,
  updates: Partial<Omit<User, 'User_id'>>
): Promise<ApiResponse<User>> => {
  try {
    // In a real app, this would make a PUT/PATCH request
    // const updatedUser = await apiClient.put<User>(`${API_CONFIG.ENDPOINTS.USERS}/${userId}`, updates);
    
    // For demo purposes, we'll simulate updating a user
    const users: User[] = await apiClient.get<User[]>(API_CONFIG.ENDPOINTS.USERS);
    const userIndex = users.findIndex(u => String(u.User_id) === String(userId));
    
    if (userIndex === -1) {
      return {
        success: false,
        error: 'User not found',
      };
    }
    
    const updatedUser = { ...users[userIndex], ...updates };
    
    return {
      success: true,
      data: updatedUser,
      message: 'User updated successfully',
    };
  } catch (error) {
    console.error('Update User Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update user',
    };
  }
};

// Delete user
export const deleteUser = async (_userId: string | number): Promise<ApiResponse<null>> => {
  try {
    // In a real app, this would make a DELETE request
    // await apiClient.delete(`${API_CONFIG.ENDPOINTS.USERS}/${userId}`);
    
    return {
      success: true,
      message: 'User deleted successfully',
    };
  } catch (error) {
    console.error('Delete User Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete user',
    };
  }
};

// Get user statistics
export const getUserStats = async (): Promise<ApiResponse<{
  totalUsers: number;
  activeUsers: number;
  usersByRole: Record<string, number>;
}>> => {
  try {
    const users: User[] = await apiClient.get<User[]>(API_CONFIG.ENDPOINTS.USERS);
    
    const stats = {
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'active' || !u.status).length,
      usersByRole: users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
    
    return {
      success: true,
      data: stats,
    };
  } catch (error) {
    console.error('Get User Stats Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user statistics',
    };
  }
};
