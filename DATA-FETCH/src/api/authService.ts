import type { User, LoginResult, ApiResponse } from '../types';
import { API_CONFIG } from '../constants';
import { apiClient, ApiError } from './client';

// Login function
export const login = async (userId: string, password: string): Promise<LoginResult> => {
  if (!userId || !password) {
    return { success: false, message: 'User ID and Password are required.' };
  }

  try {
    // Fetch users from API
    const users: User[] = await apiClient.get<User[]>(API_CONFIG.ENDPOINTS.USERS);

    // Find user by ID (case-insensitive)
    const inputIdString = String(userId).toLowerCase();
    const foundUser = users.find(
      (user) => String(user.User_id).toLowerCase() === inputIdString
    );

    if (!foundUser) {
      return { success: false, message: 'User ID not found.' };
    }

    if (foundUser.Password !== password) {
      return { success: false, message: 'Incorrect password.' };
    }

    // Generate a simple token (in a real app, this would come from the server)
    const token = btoa(`${foundUser.User_id}:${Date.now()}`);

    return {
      success: true,
      message: 'Login successful!',
      user: foundUser,
      token,
    };

  } catch (error) {
    console.error('Login Service Error:', error);
    
    if (error instanceof ApiError) {
      return { success: false, message: error.message };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : 'An unknown error occurred.',
    };
  }
};

// Logout function
export const logout = async (): Promise<ApiResponse<null>> => {
  try {
    // In a real app, you might call a logout endpoint
    // await apiClient.post(API_CONFIG.ENDPOINTS.LOGOUT);
    
    return {
      success: true,
      message: 'Logged out successfully',
    };
  } catch (error) {
    console.error('Logout Service Error:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Logout failed',
    };
  }
};

// Get current user profile
export const getCurrentUser = async (): Promise<ApiResponse<User>> => {
  try {
    // In a real app, this would fetch the current user's profile
    // const user = await apiClient.get<User>('/auth/me');
    
    // For now, we'll return a placeholder
    throw new Error('Not implemented');
    
  } catch (error) {
    console.error('Get Current User Error:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user profile',
    };
  }
};

// Update user profile
export const updateUserProfile = async (_userId: string, _updates: Partial<User>): Promise<ApiResponse<User>> => {
  try {
    // In a real app, this would update the user's profile
    // const updatedUser = await apiClient.put<User>(`/users/${userId}`, updates);
    
    throw new Error('Not implemented');
    
  } catch (error) {
    console.error('Update User Profile Error:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update user profile',
    };
  }
};

// Change password
export const changePassword = async (
  _userId: string,
  _currentPassword: string,
  _newPassword: string
): Promise<ApiResponse<null>> => {
  try {
    // In a real app, this would change the user's password
    // await apiClient.post('/auth/change-password', {
    //   userId,
    //   currentPassword,
    //   newPassword,
    // });
    
    throw new Error('Not implemented');
    
  } catch (error) {
    console.error('Change Password Error:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to change password',
    };
  }
};
