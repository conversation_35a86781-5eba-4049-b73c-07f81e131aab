// API Configuration
export const API_CONFIG = {
  BASE_URL: 'http://localhost:1240',
  ENDPOINTS: {
    USERS: '/Users',
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    DASHBOARD: '/dashboard',
  },
  TIMEOUT: 10000,
} as const;

// Application Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  ADMIN: '/admin',
  HR: '/hr',
  EMPLOYEE: '/employee',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  SETTINGS: '/settings',
} as const;

// User Roles
export const USER_ROLES = {
  ADMIN: 'Admin',
  HR: 'HR',
  EMPLOYEE: 'Employee',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  THEME: 'theme_preference',
  LANGUAGE: 'language_preference',
} as const;

// Theme Configuration
export const THEME = {
  COLORS: {
    PRIMARY: '#646cff',
    SECONDARY: '#535bf2',
    SUCCESS: '#10b981',
    DANGER: '#ef4444',
    WARNING: '#f59e0b',
    INFO: '#3b82f6',
    LIGHT: '#f8fafc',
    DARK: '#1e293b',
    BACKGROUND: '#ffffff',
    SURFACE: '#f1f5f9',
    TEXT: '#1e293b',
    TEXT_SECONDARY: '#64748b',
  },
  SPACING: {
    XS: '0.25rem',
    SM: '0.5rem',
    MD: '1rem',
    LG: '1.5rem',
    XL: '2rem',
    XXL: '3rem',
  },
  BORDER_RADIUS: {
    SM: '0.25rem',
    MD: '0.5rem',
    LG: '0.75rem',
    XL: '1rem',
  },
  SHADOWS: {
    SM: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    MD: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    LG: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  },
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
  },
} as const;

// Animation Durations
export const ANIMATIONS = {
  FAST: '150ms',
  NORMAL: '300ms',
  SLOW: '500ms',
} as const;

// Form Validation Messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  PASSWORD_MIN_LENGTH: 'Password must be at least 8 characters long',
  PASSWORD_PATTERN: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  CONFIRM_PASSWORD: 'Passwords do not match',
} as const;

// Navigation Items
export const NAV_ITEMS = [
  {
    path: ROUTES.HOME,
    label: 'Home',
    icon: 'home',
    roles: [USER_ROLES.ADMIN, USER_ROLES.HR, USER_ROLES.EMPLOYEE],
  },
  {
    path: ROUTES.DASHBOARD,
    label: 'Dashboard',
    icon: 'dashboard',
    roles: [USER_ROLES.ADMIN, USER_ROLES.HR],
  },
  {
    path: ROUTES.ADMIN,
    label: 'Admin Panel',
    icon: 'admin',
    roles: [USER_ROLES.ADMIN],
  },
  {
    path: ROUTES.HR,
    label: 'HR Management',
    icon: 'users',
    roles: [USER_ROLES.HR],
  },
  {
    path: ROUTES.EMPLOYEE,
    label: 'Employee Portal',
    icon: 'user',
    roles: [USER_ROLES.EMPLOYEE],
  },
] as const;

// Status Options
export const STATUS_OPTIONS = [
  { value: 'active', label: 'Active', color: THEME.COLORS.SUCCESS },
  { value: 'inactive', label: 'Inactive', color: THEME.COLORS.DANGER },
] as const;

// Department Options
export const DEPARTMENT_OPTIONS = [
  { value: 'engineering', label: 'Engineering' },
  { value: 'hr', label: 'Human Resources' },
  { value: 'finance', label: 'Finance' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'sales', label: 'Sales' },
  { value: 'operations', label: 'Operations' },
] as const;
