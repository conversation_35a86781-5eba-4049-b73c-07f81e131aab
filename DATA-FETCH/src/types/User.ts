// User Types for HRM System
export interface User {
  User_id: number;
  name: string;
  email: string;
  role: 'Employee' | 'HR' | 'Admin';
  Password: string;
  id?: string; // JSON server generated ID
}

export interface EmployeeProfile extends User {
  // Personal Information
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  gender?: 'Male' | 'Female' | 'Other';
  maritalStatus?: 'Single' | 'Married' | 'Divorced' | 'Widowed';
  nationality?: string;
  
  // Contact Information
  mobileNumber?: string;
  alternateEmail?: string;
  address?: Address;
  emergencyContact?: EmergencyContact;
  
  // Professional Information
  employeeId?: string;
  department?: string;
  designation?: string;
  joiningDate?: string;
  employmentType?: 'Full-time' | 'Part-time' | 'Contract' | 'Intern';
  reportingManager?: string;
  workLocation?: string;
  
  // Profile & Documents
  profilePicture?: string;
  resume?: string;
  documents?: Document[];
  
  // Compensation & Benefits
  salary?: number;
  currency?: string;
  bankDetails?: BankDetails;
  
  // Skills & Education
  skills?: string[];
  education?: Education[];
  certifications?: Certification[];
  
  // System Information
  isActive?: boolean;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phoneNumber: string;
  email?: string;
}

export interface Document {
  id: string;
  name: string;
  type: 'ID_PROOF' | 'ADDRESS_PROOF' | 'EDUCATION' | 'EXPERIENCE' | 'OTHER';
  url: string;
  uploadedAt: string;
}

export interface BankDetails {
  accountNumber: string;
  bankName: string;
  ifscCode: string;
  accountHolderName: string;
}

export interface Education {
  id: string;
  degree: string;
  institution: string;
  fieldOfStudy: string;
  startYear: number;
  endYear: number;
  grade?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

// Form Types
export interface ProfileFormData {
  personalInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    maritalStatus: string;
    nationality: string;
  };
  contactInfo: {
    mobileNumber: string;
    alternateEmail: string;
    address: Address;
    emergencyContact: EmergencyContact;
  };
  professionalInfo: {
    department: string;
    designation: string;
    employmentType: string;
    workLocation: string;
  };
}
