import React, { useState } from 'react';
import './App.css';

// User interface
interface User {
  id: string;
  name: string;
  role: 'employee' | 'admin' | 'hr';
  email: string;
  department: string;
  position: string;
  avatar?: string;
  joinDate: string;
}

// Demo users
const DEMO_USERS: User[] = [
  {
    id: 'emp001',
    name: '<PERSON>',
    role: 'employee',
    email: '<EMAIL>',
    department: 'Engineering',
    position: 'Software Developer',
    joinDate: '2022-01-15'
  },
  {
    id: 'admin001',
    name: '<PERSON>',
    role: 'admin',
    email: '<EMAIL>',
    department: 'Administration',
    position: 'System Administrator',
    joinDate: '2020-03-10'
  },
  {
    id: 'hr001',
    name: '<PERSON>',
    role: 'hr',
    email: '<EMAIL>',
    department: 'Human Resources',
    position: 'HR Manager',
    joinDate: '2021-06-20'
  }
];

const App: React.FC = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState('login');

  // Login handler
  const handleLogin = async (userId: string, password: string): Promise<boolean> => {
    setIsLoading(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    const user = DEMO_USERS.find(u => u.id.toLowerCase() === userId.toLowerCase());

    if (user && password === 'password123') {
      setCurrentUser(user);
      setCurrentPage('dashboard');
      setIsLoading(false);
      return true;
    }

    setIsLoading(false);
    return false;
  };

  // Logout handler
  const handleLogout = () => {
    setCurrentUser(null);
    setCurrentPage('login');
  };

  // Render login page
  const renderLogin = () => (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="logo">
            <div className="logo-icon">🏢</div>
            <h1>HRM Portal</h1>
          </div>
          <p>Welcome to Human Resource Management System</p>
        </div>

        <LoginForm onLogin={handleLogin} isLoading={isLoading} />

        <div className="demo-section">
          <h3>Demo Accounts</h3>
          <div className="demo-users">
            {DEMO_USERS.map(user => (
              <div key={user.id} className={`demo-user ${user.role}`}>
                <div className="demo-info">
                  <strong>{user.id}</strong>
                  <span>{user.name}</span>
                  <span className="role">{user.role.toUpperCase()}</span>
                </div>
              </div>
            ))}
          </div>
          <p className="demo-note">Password: <strong>password123</strong></p>
        </div>
      </div>
    </div>
  );

  // Render dashboard based on user role
  const renderDashboard = () => {
    if (!currentUser) return null;

    return (
      <div className="dashboard">
        <Header user={currentUser} onLogout={handleLogout} />
        <div className="dashboard-content">
          <Sidebar user={currentUser} currentPage={currentPage} setCurrentPage={setCurrentPage} />
          <MainContent user={currentUser} currentPage={currentPage} />
        </div>
      </div>
    );
  };

  return (
    <div className="app">
      {currentUser ? renderDashboard() : renderLogin()}
    </div>
  );
};

// Login Form Component
const LoginForm: React.FC<{ onLogin: (id: string, pass: string) => Promise<boolean>, isLoading: boolean }> = ({ onLogin, isLoading }) => {
  const [userId, setUserId] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!userId || !password) {
      setError('Please fill in all fields');
      return;
    }

    const success = await onLogin(userId, password);
    if (!success) {
      setError('Invalid credentials. Please try again.');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <div className="form-group">
        <label>User ID</label>
        <input
          type="text"
          value={userId}
          onChange={(e) => setUserId(e.target.value)}
          placeholder="Enter your User ID"
          disabled={isLoading}
        />
      </div>

      <div className="form-group">
        <label>Password</label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Enter your password"
          disabled={isLoading}
        />
      </div>

      {error && <div className="error-message">{error}</div>}

      <button type="submit" className="login-btn" disabled={isLoading}>
        {isLoading ? (
          <>
            <div className="spinner"></div>
            Signing In...
          </>
        ) : (
          'Sign In'
        )}
      </button>
    </form>
  );
};

// Header Component
const Header: React.FC<{ user: User, onLogout: () => void }> = ({ user, onLogout }) => (
  <header className="header">
    <div className="header-left">
      <div className="logo">
        <div className="logo-icon">🏢</div>
        <span>HRM Portal</span>
      </div>
    </div>
    <div className="header-right">
      <div className="user-info">
        <div className="user-avatar">{user.name.charAt(0)}</div>
        <div className="user-details">
          <span className="user-name">{user.name}</span>
          <span className="user-role">{user.role.toUpperCase()}</span>
        </div>
      </div>
      <button onClick={onLogout} className="logout-btn">
        <span>🚪</span> Logout
      </button>
    </div>
  </header>
);

// Sidebar Component
const Sidebar: React.FC<{ user: User, currentPage: string, setCurrentPage: (page: string) => void }> = ({ user, currentPage, setCurrentPage }) => {
  const getMenuItems = () => {
    const commonItems = [
      { id: 'dashboard', label: 'Dashboard', icon: '📊' },
      { id: 'profile', label: 'My Profile', icon: '👤' }
    ];

    switch (user.role) {
      case 'employee':
        return [
          ...commonItems,
          { id: 'attendance', label: 'Attendance', icon: '📅' },
          { id: 'leave', label: 'Leave Management', icon: '🏖️' },
          { id: 'payroll', label: 'Payroll', icon: '💰' },
          { id: 'documents', label: 'Documents', icon: '📄' }
        ];
      case 'hr':
        return [
          ...commonItems,
          { id: 'employees', label: 'Employee Management', icon: '👥' },
          { id: 'recruitment', label: 'Recruitment', icon: '🤝' },
          { id: 'performance', label: 'Performance', icon: '📈' },
          { id: 'reports', label: 'Reports', icon: '📊' }
        ];
      case 'admin':
        return [
          ...commonItems,
          { id: 'users', label: 'User Management', icon: '⚙️' },
          { id: 'system', label: 'System Settings', icon: '🔧' },
          { id: 'analytics', label: 'Analytics', icon: '📈' },
          { id: 'security', label: 'Security', icon: '🔒' }
        ];
      default:
        return commonItems;
    }
  };

  return (
    <aside className="sidebar">
      <nav className="sidebar-nav">
        {getMenuItems().map(item => (
          <button
            key={item.id}
            className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
            onClick={() => setCurrentPage(item.id)}
          >
            <span className="nav-icon">{item.icon}</span>
            <span className="nav-label">{item.label}</span>
          </button>
        ))}
      </nav>
    </aside>
  );
};

// Main Content Component
const MainContent: React.FC<{ user: User, currentPage: string }> = ({ user, currentPage }) => {
  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return <DashboardContent user={user} />;
      case 'profile':
        return <ProfileContent user={user} />;
      case 'attendance':
        return <AttendanceContent user={user} />;
      case 'leave':
        return <LeaveContent user={user} />;
      case 'payroll':
        return <PayrollContent user={user} />;
      case 'employees':
        return <EmployeesContent user={user} />;
      case 'recruitment':
        return <RecruitmentContent user={user} />;
      case 'users':
        return <UsersContent user={user} />;
      case 'analytics':
        return <AnalyticsContent user={user} />;
      default:
        return <DashboardContent user={user} />;
    }
  };

  return (
    <main className="main-content">
      {renderContent()}
    </main>
  );
};

// Dashboard Content
const DashboardContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="dashboard-header-new">
      <div className="dashboard-title">
        <h1>Overview</h1>
        <div className="breadcrumb">
          <span>Dashboard</span> / <span>Overview</span>
        </div>
      </div>
      <div className="dashboard-actions">
        <button className="btn-secondary">Export</button>
        <button className="btn-primary">Add New</button>
      </div>
    </div>

    <div className="overview-grid">
      <div className="overview-stats">
        <div className="stat-card-new">
          <div className="stat-number">101</div>
          <div className="stat-label">People</div>
          <div className="stat-change positive">+5</div>
        </div>
        <div className="stat-card-new">
          <div className="stat-number">20</div>
          <div className="stat-label">Departments</div>
          <div className="stat-icon-small">🏢</div>
        </div>
        <div className="stat-card-new">
          <div className="stat-number">9</div>
          <div className="stat-label">New Joining</div>
          <div className="stat-icon-small">👥</div>
        </div>
      </div>

      <div className="attendance-widget">
        <div className="widget-header">
          <h3>Attendance Self Service</h3>
          <select className="time-selector">
            <option>This Month</option>
            <option>Last Month</option>
          </select>
        </div>
        <div className="attendance-display">
          <div className="time-display">10:50:18 <span>AM</span></div>
          <div className="shift-info">
            <div className="shift-item">
              <span className="label">General Shift:</span>
              <span className="value">9:00 AM - 6:00 PM</span>
            </div>
            <div className="shift-item">
              <span className="label">Check-in Time:</span>
              <span className="value">8:40:18 AM</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div className="announcements-section">
      <div className="section-header">
        <h3>Latest Announcement</h3>
        <button className="see-more">See more</button>
      </div>
      <div className="announcement-item">
        <div className="announcement-avatar">E</div>
        <div className="announcement-content">
          <div className="announcement-title">Eid Ul Adha Holiday-2022</div>
          <div className="announcement-meta">
            <span className="announcement-author">Admin</span>
            <span className="announcement-date">Jun 15, 2023</span>
          </div>
        </div>
      </div>
    </div>

    <div className="quick-actions-new">
      <h3>Quick Actions</h3>
      <div className="action-grid">
        <button className="action-card">
          <div className="action-icon">📝</div>
          <span>Request Leave</span>
        </button>
        <button className="action-card">
          <div className="action-icon">📊</div>
          <span>View Reports</span>
        </button>
        <button className="action-card">
          <div className="action-icon">👥</div>
          <span>Team Directory</span>
        </button>
        <button className="action-card">
          <div className="action-icon">💰</div>
          <span>Payroll</span>
        </button>
      </div>
    </div>
  </div>
);

// Profile Content
const ProfileContent: React.FC<{ user: User }> = ({ user }) => (
  <div className="content-section">
    <div className="profile-layout">
      <div className="profile-main">
        <div className="profile-header-new">
          <h1>Employee Profile</h1>
          <button className="invite-btn">Invite to Job</button>
        </div>

        <div className="profile-sections">
          <div className="portfolio-section">
            <div className="section-header">
              <h3>Portfolio</h3>
              <button className="view-all">View all →</button>
            </div>
            <div className="portfolio-grid">
              <div className="portfolio-item">
                <div className="portfolio-preview">
                  <div className="portfolio-placeholder">📊</div>
                </div>
                <div className="portfolio-info">
                  <h4>Performance Dashboard</h4>
                  <p>Analytics and reporting interface</p>
                </div>
              </div>
              <div className="portfolio-item">
                <div className="portfolio-preview">
                  <div className="portfolio-placeholder">📱</div>
                </div>
                <div className="portfolio-info">
                  <h4>Mobile HR App</h4>
                  <p>Employee self-service mobile application</p>
                </div>
              </div>
              <div className="portfolio-item">
                <div className="portfolio-preview">
                  <div className="portfolio-placeholder">📈</div>
                </div>
                <div className="portfolio-info">
                  <h4>Analytics Platform</h4>
                  <p>Real-time data visualization dashboard</p>
                </div>
              </div>
            </div>
          </div>

          <div className="skills-section">
            <h3>Skills</h3>
            <div className="skills-grid">
              <span className="skill-tag">Leadership</span>
              <span className="skill-tag">Project Management</span>
              <span className="skill-tag">Team Building</span>
              <span className="skill-tag">Strategic Planning</span>
              <span className="skill-tag">Communication</span>
              <span className="skill-tag">Problem Solving</span>
              <span className="skill-tag">Data Analysis</span>
              <span className="skill-tag">Process Improvement</span>
            </div>
          </div>

          <div className="recommendations-section">
            <h3>Recommendations</h3>
            <div className="recommendation-item">
              <div className="recommender-avatar">L</div>
              <div className="recommendation-content">
                <div className="recommender-info">
                  <strong>Leon Ward, CEO</strong>
                </div>
                <p>It was so good to work with {user.name}. They're very creative and professional...</p>
                <button className="view-all-small">View all →</button>
              </div>
            </div>
            <div className="recommendation-item">
              <div className="recommender-avatar">K</div>
              <div className="recommendation-content">
                <div className="recommender-info">
                  <strong>Keith Scott, Project Manager</strong>
                </div>
                <p>{user.name} is a super-talented individual with a constant eager to learn...</p>
              </div>
            </div>
          </div>

          <div className="documents-section">
            <h3>Documents</h3>
            <div className="document-list">
              <div className="document-item">
                <div className="document-icon">📄</div>
                <div className="document-info">
                  <span className="document-name">Resume.pdf</span>
                  <span className="document-size">1.26 MB</span>
                </div>
                <button className="download-icon">⬇️</button>
              </div>
              <div className="document-item">
                <div className="document-icon">📄</div>
                <div className="document-info">
                  <span className="document-name">Cover letter.pdf</span>
                  <span className="document-size">1.49 MB</span>
                </div>
                <button className="download-icon">⬇️</button>
              </div>
              <div className="document-item">
                <div className="document-icon">📄</div>
                <div className="document-info">
                  <span className="document-name">Bio.pdf</span>
                  <span className="document-size">1.59 MB</span>
                </div>
                <button className="download-icon">⬇️</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="profile-sidebar">
        <div className="profile-card-new">
          <div className="profile-avatar-new">
            <img src="/api/placeholder/80/80" alt={user.name} className="avatar-img" />
          </div>
          <h2>{user.name}</h2>
          <p className="profile-title">{user.position}</p>
          <p className="profile-description">
            Experienced {user.position.toLowerCase()} with a strong background in {user.department.toLowerCase()},
            team management, and strategic planning.
          </p>
          <div className="contact-icons">
            <button className="contact-icon">📞</button>
            <button className="contact-icon">✉️</button>
            <button className="contact-icon">💬</button>
            <button className="contact-icon">📋</button>
          </div>
        </div>

        <div className="experience-section">
          <h3>Experience</h3>
          <div className="experience-item">
            <div className="company-logo">G</div>
            <div className="experience-details">
              <h4>Google</h4>
              <p>Oct 2018 - Present</p>
              <span className="location">📍 California</span>
              <div className="role-info">
                <strong>Lead {user.position}</strong>
                <p>Designed full features of Bartmobile Autopilot user flows, high-fidelity mockups.</p>
              </div>
            </div>
          </div>
          <div className="experience-item">
            <div className="company-logo">F</div>
            <div className="experience-details">
              <h4>Facebook</h4>
              <p>Jan 2016 - Jun 2018</p>
              <span className="location">📍 Poland</span>
              <div className="role-info">
                <strong>Senior {user.position}</strong>
                <p>Building new features and improving existing user flows of the mobile and web apps.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Attendance Content
const AttendanceContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="page-header">
      <h1>📅 Attendance</h1>
      <p>Track your attendance and working hours</p>
    </div>

    <div className="attendance-summary">
      <div className="summary-card">
        <h3>This Month</h3>
        <div className="summary-stats">
          <div className="summary-item">
            <span className="summary-number">22</span>
            <span className="summary-label">Present</span>
          </div>
          <div className="summary-item">
            <span className="summary-number">2</span>
            <span className="summary-label">Absent</span>
          </div>
          <div className="summary-item">
            <span className="summary-number">8.5</span>
            <span className="summary-label">Avg Hours</span>
          </div>
        </div>
      </div>
    </div>

    <div className="attendance-table">
      <h3>Recent Records</h3>
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Check In</th>
            <th>Check Out</th>
            <th>Hours</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>2024-01-15</td>
            <td>09:00 AM</td>
            <td>05:30 PM</td>
            <td>8.5h</td>
            <td><span className="status present">Present</span></td>
          </tr>
          <tr>
            <td>2024-01-14</td>
            <td>09:15 AM</td>
            <td>05:45 PM</td>
            <td>8.5h</td>
            <td><span className="status present">Present</span></td>
          </tr>
          <tr>
            <td>2024-01-13</td>
            <td>-</td>
            <td>-</td>
            <td>-</td>
            <td><span className="status weekend">Weekend</span></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
);

// Leave Content
const LeaveContent: React.FC<{ user: User }> = ({ user: _user }) => {
  const [leaveForm, setLeaveForm] = useState({
    type: 'vacation',
    startDate: '',
    endDate: '',
    reason: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('Leave request submitted successfully!');
    setLeaveForm({ type: 'vacation', startDate: '', endDate: '', reason: '' });
  };

  return (
    <div className="content-section">
      <div className="page-header">
        <h1>🏖️ Leave Management</h1>
        <p>Request and manage your leave applications</p>
      </div>

      <div className="leave-balance">
        <h3>Leave Balance</h3>
        <div className="balance-cards">
          <div className="balance-card">
            <h4>Vacation</h4>
            <div className="balance-info">
              <span className="used">5 used</span>
              <span className="remaining">15 remaining</span>
            </div>
            <div className="balance-bar">
              <div className="balance-fill" style={{ width: '25%' }}></div>
            </div>
          </div>
          <div className="balance-card">
            <h4>Sick Leave</h4>
            <div className="balance-info">
              <span className="used">2 used</span>
              <span className="remaining">8 remaining</span>
            </div>
            <div className="balance-bar">
              <div className="balance-fill" style={{ width: '20%' }}></div>
            </div>
          </div>
        </div>
      </div>

      <div className="leave-request">
        <h3>Request Leave</h3>
        <form onSubmit={handleSubmit} className="leave-form">
          <div className="form-row">
            <div className="form-group">
              <label>Leave Type</label>
              <select
                value={leaveForm.type}
                onChange={(e) => setLeaveForm({...leaveForm, type: e.target.value})}
              >
                <option value="vacation">Vacation</option>
                <option value="sick">Sick Leave</option>
                <option value="personal">Personal</option>
              </select>
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Start Date</label>
              <input
                type="date"
                value={leaveForm.startDate}
                onChange={(e) => setLeaveForm({...leaveForm, startDate: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <label>End Date</label>
              <input
                type="date"
                value={leaveForm.endDate}
                onChange={(e) => setLeaveForm({...leaveForm, endDate: e.target.value})}
                required
              />
            </div>
          </div>
          <div className="form-group">
            <label>Reason</label>
            <textarea
              value={leaveForm.reason}
              onChange={(e) => setLeaveForm({...leaveForm, reason: e.target.value})}
              placeholder="Please provide a reason for your leave request"
              rows={3}
              required
            />
          </div>
          <button type="submit" className="submit-btn">Submit Request</button>
        </form>
      </div>
    </div>
  );
};

// Payroll Content
const PayrollContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="page-header">
      <h1>💰 Payroll</h1>
      <p>View your salary and payroll information</p>
    </div>

    <div className="payroll-summary">
      <div className="salary-card">
        <h3>Current Salary Breakdown</h3>
        <div className="salary-details">
          <div className="salary-item">
            <span>Basic Salary:</span>
            <span>$75,000</span>
          </div>
          <div className="salary-item">
            <span>Allowances:</span>
            <span className="positive">+$15,000</span>
          </div>
          <div className="salary-item">
            <span>Deductions:</span>
            <span className="negative">-$8,000</span>
          </div>
          <div className="salary-item total">
            <span>Net Salary:</span>
            <span>$82,000</span>
          </div>
        </div>
      </div>
    </div>

    <div className="payslip-history">
      <h3>Recent Payslips</h3>
      <div className="payslip-list">
        <div className="payslip-item">
          <span>January 2024</span>
          <span>$82,000</span>
          <button className="download-btn">📄 Download</button>
        </div>
        <div className="payslip-item">
          <span>December 2023</span>
          <span>$82,000</span>
          <button className="download-btn">📄 Download</button>
        </div>
      </div>
    </div>
  </div>
);

// Placeholder components for other roles
const EmployeesContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="page-header">
      <h1>👥 Employee Management</h1>
      <p>Manage employee records and information</p>
    </div>
    <div className="placeholder">
      <h3>Employee management features coming soon...</h3>
      <p>This section will include employee directory, performance tracking, and more.</p>
    </div>
  </div>
);

const RecruitmentContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="page-header">
      <h1>🤝 Recruitment</h1>
      <p>Manage job postings and candidate applications</p>
    </div>
    <div className="placeholder">
      <h3>Recruitment features coming soon...</h3>
      <p>This section will include job postings, candidate tracking, and interview scheduling.</p>
    </div>
  </div>
);

const UsersContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="page-header">
      <h1>⚙️ User Management</h1>
      <p>Manage system users and permissions</p>
    </div>
    <div className="placeholder">
      <h3>User management features coming soon...</h3>
      <p>This section will include user creation, role assignment, and permission management.</p>
    </div>
  </div>
);

const AnalyticsContent: React.FC<{ user: User }> = ({ user: _user }) => (
  <div className="content-section">
    <div className="page-header">
      <h1>📈 Analytics</h1>
      <p>View system analytics and reports</p>
    </div>
    <div className="placeholder">
      <h3>Analytics features coming soon...</h3>
      <p>This section will include charts, reports, and data visualization.</p>
    </div>
  </div>
);

export default App;
